{"name": "vip_admin_server", "version": "1.0.0", "description": "vip_admin_server", "private": true, "egg": {"typescript": true}, "scripts": {"start": "egg-scripts start --daemon --title=egg-server-wishNodeServe", "stop": "egg-scripts stop --title=egg-server-wishNodeServe", "dev": "egg-bin dev", "debug": "egg-bin debug", "test-local": "egg-bin test -p", "test": "npm run lint -- --fix && npm run test-local", "cov": "egg-bin cov -p", "ci": "npm run lint && npm run cov && npm run tsc && npm run clean", "lint": "eslint . --ext .ts --cache", "tsc": "tsc", "clean": "tsc -b --clean"}, "dependencies": {"@eggjs/tegg": "^3.5.2", "@eggjs/tegg-aop-plugin": "^3.5.2", "@eggjs/tegg-config": "^3.2.3", "@eggjs/tegg-controller-plugin": "^3.5.2", "@eggjs/tegg-eventbus-plugin": "^3.5.2", "@eggjs/tegg-plugin": "^3.5.2", "@eggjs/tegg-schedule-plugin": "^3.5.2", "@types/crypto-js": "^4.2.1", "argon2": "^0.41.1", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "egg": "^3.15.0", "egg-mysql": "^4.0.0", "egg-scripts": "^2.17.0", "egg-tracer": "^2.0.0", "egg-validate": "^2.0.2", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1"}, "devDependencies": {"@eggjs/tsconfig": "1", "@types/mocha": "10", "@types/node": "18", "egg-bin": "6", "egg-mock": "5", "eslint": "8", "eslint-config-egg": "12", "typescript": "4"}, "engines": {"node": ">=18.0.0"}, "resolutions": {"undici": "5.26.3"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "main": "index.js", "directories": {"test": "test"}, "keywords": []}