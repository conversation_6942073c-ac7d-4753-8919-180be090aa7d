import { EggAppConfig, EggAppInfo, PowerPartial } from 'egg';

export default (appInfo: EggAppInfo) => {
  const config = {} as PowerPartial<EggAppConfig>;

  // override config from framework / plugin
  // use for cookie sign key, should change to your own and keep security
  config.keys = appInfo.name + '<EMAIL>';

  // add your egg config in here
  config.middleware = [ 'jwtCheck', 'rateLimit', 'paramCheck', 'logRequest', 'errorHandler' ];

  // 只对 / api 前缀的 url 路径生效
  config.errorHandler = {
    match: '/api',
  };

  config.rateLimit = {
    limit: 200, // 每分钟允许300次请求
    interval: 60 * 1000, // 时间间隔为1分钟
  };

  config.logger = {
    dir: `${appInfo.baseDir}/logs`, // 日志存储目录
    level: 'INFO', // 日志级别
    consoleLevel: 'NONE', // 控制台日志级别
    outputJSON: true, // 输出 JSON 格式的日志
  };

  // 生产环境配置
  if (appInfo.env === 'prod') {
    config.logger.consoleLevel = 'NONE'; // 生产环境不打印控制台日志
  }

  config.security = {
    ...config.security,
    csrf: {
      ...config.security?.csrf,
      maxAge: (3600000 * 2) + (5 * 60 * 1000),
    },
  };

  // config.security = {
  //   csrf: {
  //     ignore: ctx => {
  //       if (ctx.request.url.includes('login') || ctx.request.url.includes('refresh')) {
  //         return true;
  //       } else {
  //         return false;
  //       }
  //     },
  //   },
  // };

  // add your special config in here
  const bizConfig = {
    sourceUrl: `https://github.com/eggjs/examples/tree/master/${appInfo.name}`,
  };

  // the return config will combines to EggAppConfig
  return {
    ...config,
    ...bizConfig,
  };
};
