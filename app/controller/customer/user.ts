import { Controller } from 'egg';
import { WxSecretKey, tokenType } from './../../config/secret';
import { ONE_HOUR, ONE_DAY } from './../../config/const';
import { decryptedText } from './../../utils';
import { successRes, failRes } from './../../utils/response';
import argon2 from 'argon2';
import { v4 as uuidv4 } from 'uuid';
import jwt from 'jsonwebtoken';
export default class login extends Controller {

  async getKey() {
    const { ctx } = this;
    successRes({
      ctx,
      data: { xkey: ctx.csrf },
    });
  }

  async customerLogin() {
    const { ctx } = this;
    try {
      ctx.validate({ username: 'string', password: 'string' }, ctx.request.body);
      const { username, password }: { username: string, password: string } = ctx.request.body;
      const usernameOrPhoneNumber = username.trim(); // 清除可能的空格

      // 定义正则表达式匹配用户名和手机号码格式
      const phoneNumberRegex = /^\d{11}$/;

      let isPhoneNumber = false;

      // 检查是否符合手机号码格式
      if (phoneNumberRegex.test(usernameOrPhoneNumber)) {
        isPhoneNumber = true;
      } else {
        isPhoneNumber = false;
      }

      let userinfo: any;
      if (isPhoneNumber) {
        // 如果是手机号码
        userinfo = await ctx.service.customer.user.getUserInfoByPhone(usernameOrPhoneNumber);
      } else {
        // 如果是用户名
        userinfo = await ctx.service.customer.user.getUserInfoByUsername(usernameOrPhoneNumber);
      }

      if (!userinfo) {
        failRes({
          ctx,
          message: '账号或密码错误',
          code: 401,
        });
        return;
      }

      const text = decryptedText(password);
      const isValidPassword = await argon2.verify(userinfo.password, text);

      if (!isValidPassword) {
        failRes({
          ctx,
          message: '账号或密码错误',
          code: 400,
        });
        return;
      }
      if (userinfo.state !== 0) {
        failRes({
          ctx,
          message: '账户异常,请联系管理员,code:' + userinfo.state,
          code: 401,
        });
        return;
      }
      const payload = {
        user_id: userinfo.customer_id,
        token_type: tokenType.customer,
        exp: +new Date() + ONE_HOUR,
        iat: +new Date(),
        jti: uuidv4(),
      };
      const token = jwt.sign(payload, WxSecretKey);
      const refresh = jwt.sign(
        {
          ...payload,
          token_type: tokenType.customer + '_refresh',
          exp: +new Date() + ONE_DAY,
        },
        WxSecretKey,
      );
      successRes({
        ctx,
        data: {
          access: token,
          refresh,
        },
      });

      ctx.service.customer.user.updateUserInfoLastDate(userinfo.user_id);

    } catch (err: any) {
      failRes({ ctx, message: '登录失败，请稍后重试' });
    }
  }

  async customerUserProfilie() {
    const { ctx } = this;
    try {
      const userId = ctx.state.user_id; // 从JWT Token中获取用户ID

      const userinfo = await ctx.service.customer.user.getUserInfoById(userId);
      if (userinfo) {
        successRes({
          ctx,
          data: {
            customer_id: userinfo.customer_id,
            customer_name: userinfo.customer_name,
            avatar: userinfo.avatar,
            username: userinfo.username,
            phone: userinfo.phone,
            email: userinfo.email,
            id_card_id: userinfo.id_card_id,
            id_card_images: userinfo.id_card_images,
            note: userinfo.note,
            address: userinfo.address,
            create_people: userinfo.create_people,
            create_people_id: userinfo.create_people_id,
            create_date: userinfo.create_date,
            state: userinfo.state,
            gender: userinfo.gender,
            city: userinfo.city,
            type: userinfo.type,
            update_date: userinfo.update_date,
          },
        });
      } else {
        failRes({ ctx, data: null, message: '用户不存在', code: 404 });
      }
    } catch (err) {
      failRes({ ctx, message: '获取用户详情失败，请稍后重试' });
    }
  }


  async updatePassword() {
    const { ctx } = this;
    try {
      ctx.validate({ old_password: 'string', new_password: 'string' }, ctx.request.body);
      const { old_password, new_password }: { old_password: string, new_password: string } = ctx.request.body;

      if (!ctx?.state?.user_id || !new_password) {
        failRes({ ctx, message: '修改失败' });
        return;
      }

      const userId = ctx.state.user_id; // 从JWT Token中获取用户ID
      const userinfo = await ctx.service.customer.user.getUserInfoById(userId);

      if (!userinfo) {
        failRes({ ctx, message: '用户不存在', code: 404 });
        return;
      }
      const oldPassword = decryptedText(old_password);
      const isValidPassword = await argon2.verify(userinfo.password, oldPassword);

      if (!isValidPassword) {
        failRes({
          ctx,
          message: '旧密码错误',
          code: 400,
        });
        return;
      }

      const newPassword = decryptedText(new_password);
      const hashedNewPassword = await argon2.hash(newPassword);
      const result = await ctx.service.customer.user.updateUserPassword(userId, hashedNewPassword);
      if (result) {
        successRes({ ctx, message: '密码修改成功' });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      console.log(err, 'result');
      failRes({ ctx, message: '修改失败' });
    }
  }

  async refreshCustomerUserToken() {
    const { ctx } = this;
    try {
      ctx.validate({ rkey: 'string' }, ctx.request.body);
      const jwtData = jwt.verify(ctx.request.body.rkey, WxSecretKey);
      const { user_id, exp, token_type } = jwtData;

      if (!jwtData || !user_id || !token_type.includes('_refresh')) {
        failRes({ ctx, message: 'token error code : 2', code: 401 });
        return;
      }

      const userinfo = await ctx.service.customer.user.getUserInfoById(user_id);

      if (!userinfo) {
        failRes({ ctx, message: '用户不存在', code: 401 });
        return;
      }

      if (userinfo.state !== 0) {
        failRes({ ctx, message: '账户异常,请联系管理员,code:' + userinfo.state, code: 401 });
        return;
      }

      if ((exp - +new Date()) > 0) {
        const payload = {
          user_id,
          token_type: tokenType.customer,
          exp: +new Date() + ONE_HOUR,
          iat: +new Date(),
          jti: uuidv4(),
        };

        const token = jwt.sign(payload, WxSecretKey);
        const refresh = jwt.sign(
          {
            ...payload,
            token_type: tokenType.customer + '_refresh',
            exp: +new Date() + ONE_DAY,
          },
          WxSecretKey,
        );

        successRes({
          ctx,
          data: {
            access: token,
            refresh,
          },
        });
      } else {
        // 过期
        failRes({ ctx, message: 'token error code : 1', code: 401 });
      }
    } catch (err: any) {
      failRes({ ctx, code: 401, message: '登录过期' });
    }
  }

}
