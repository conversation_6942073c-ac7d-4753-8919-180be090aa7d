import { Controller } from 'egg';
import { successRes, failRes } from '../../utils/response';

export default class Order extends Controller {

  async getOrderStatistics() {
    const { ctx } = this;
    try {
      const customer_id = ctx.state.user_id;
      const orderStatistics = await ctx.service.customer.order.getOrderStatistics(customer_id);
      successRes({ ctx, data: orderStatistics });
    } catch (error) {
      failRes({ ctx, message: '查询失败' });
    }
  }

  async getOrderList() {
    const { ctx } = this;
    try {
      const { current = 1, pageSize = 20, created_at, order_status } = ctx.query;
      const customer_id = ctx.state.user_id;

      // 构建查询条件
      const queryParams: {
        time_filter?: { start_time: string; end_time: string };
        order_status?: string;
      } = {};

      // 处理订单状态筛选
      if (order_status) {
        queryParams.order_status = order_status as string;
      }

      // 处理时间范围筛选 - 参考 getProductList 的处理方式
      if (created_at && typeof created_at === 'string') {
        try {
          const date = JSON.parse(created_at);
          if (Array.isArray(date) && date.length === 2 && date[0] && date[1]) {
            queryParams.time_filter = {
              start_time: date[0],
              end_time: date[1],
            };
          }
        } catch (e) {
          // 解析失败时忽略时间筛选
          ctx.logger.error('Time range parse error:', e);
        }
      }

      const orderList = await ctx.service.customer.order.getOrderList(
        customer_id,
        Number(current),
        Number(pageSize),
        queryParams,
      );

      successRes({ ctx, data: orderList });
    } catch (error) {
      failRes({ ctx, message: '查询失败' });
    }
  }
}
