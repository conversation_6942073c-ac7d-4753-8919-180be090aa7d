import { Controller } from 'egg';
import { successRes, failRes } from './../../utils/response';

export default class CustomerBox extends Controller {

  async getBoxList() {
    const { ctx } = this;
    try {
      ctx.validate({ current: 'number', pageSize: 'number' }, ctx.query);
      const customer_id = ctx.state.user_id;
      const { current = 0, pageSize = 0 } = ctx.query;

      const boxList = await ctx.service.customer.box.getBoxList(customer_id, Number(current), Number(pageSize));
      successRes({
        ctx,
        data: {
          ...boxList,
          current: Number(current),
          pageSize: Number(pageSize),
        },
      });
    } catch (err) {
      failRes({ ctx, message: '查询错误' });
    }
  }

  async getBoxDetails() {
    const { ctx } = this;
    try {
      ctx.validate({ box_id: 'number' }, ctx.query);
      const { box_id } = ctx.query;
      const boxDetails = await ctx.service.customer.box.getBoxDetails(box_id);
      successRes({
        ctx,
        data: boxDetails,
      });
    } catch (err) {
      failRes({ ctx, message: '查询错误' });
    }
  }

  async getBoxLog() {
    const { ctx } = this;
    try {
      ctx.validate({ box_id: 'number' }, ctx.query);
      const { box_id, current = 0, pageSize = 0 } = ctx.query;
      const boxLog = await ctx.service.customer.box.getBoxLog(box_id, Number(current), Number(pageSize));
      successRes({
        ctx,
        data: {
          ...boxLog,
          current: Number(current),
          pageSize: Number(pageSize),
        },
      });

    } catch (err) {
      failRes({ ctx, message: '查询错误' });
    }
  }
}
