import { Controller } from 'egg';
import { successRes, failRes } from './../../utils/response';
export default class product extends Controller {
  async getProductList() {
    const { ctx } = this;
    const {
      pageSize = 20,
      current = 1,
      product_name,
      code,
      category,
      distribution_price,
      quantity,
      style,
      orders,
    }: {
      pageSize?: number;
      current?: number;
      product_name?: string;
      code?: string;
      category?: string; // 产品类别
      distribution_price?: string,
      style?: number; // 产品风格
      quantity?: string,
      orders?: string
    } = ctx.query;

    try {
      const ordersStr = orders ? JSON.parse(orders) : {};

      let whereClause = ' WHERE 1 = 1';
      if (product_name) {
        whereClause += ` AND product.product_name LIKE '%${product_name}%'`;
      }
      if (code) {
        whereClause += ` AND product.code = ${code}`;
      }
      if (style) {
        whereClause += ` AND product.style = ${style}`;
      }
      if (category) {
        whereClause += ` AND JSON_CONTAINS(product.category, '${category}')`;
      }
      if (distribution_price) {
        const price = JSON.parse(distribution_price);
        whereClause += ` AND product_sku.distribution_price BETWEEN ${price?.[0] || 0} AND ${price?.[1] || 0}`;
      }
      if (quantity) {
        const quantity_num = JSON.parse(quantity);
        whereClause += ` AND product_sku.stock_quantity BETWEEN ${quantity_num?.[0] || 0} AND ${quantity_num?.[1] || 0}`;
      }

      whereClause += ' AND product.state = \'0\'';
      whereClause += ' AND product.open = \'1\'';

      const sort = ordersStr && Object.keys(ordersStr).length > 0 ? {
        type: Object.keys(ordersStr)[0].includes('date') ? 'date' : 'other',
        name: Object.keys(ordersStr)[0],
        value: ordersStr[Object.keys(ordersStr)[0]] === 'ascend' ? 'ASC' : 'DESC',
      } : void 0;

      let sortClause = '';
      if (sort && sort.type === 'other') {
        sortClause += ` ORDER BY ${sort.name} ${sort.value}`;
      }

      if (sort && sort.type === 'date') {
        sortClause += ` ORDER BY ${sort.name} ${sort.value}`;
      }

      const offset = (current - 1) * pageSize;

      const sql = `
                    SELECT 
                      product.product_id,
                      product.product_name,
                      product.main_images,
                      product.code,
                      product.category,
                      product.style,
                      product.inlay,
                      product.location,
                      product.create_date,
                      product.update_date,
                      product.spec_lists,
                      JSON_ARRAYAGG(
                        JSON_OBJECT(
                          'sku_id', product_sku.sku_id,
                          'sku_code', product_sku.sku_code,
                          'distribution_price', product_sku.distribution_price,
                          'stock_quantity', product_sku.stock_quantity,
                          'weight', product_sku.weight,
                          'size', product_sku.size,
                          'image', product_sku.image,
                          'state', product_sku.state,
                          'specs', product_sku.specs,
                          'created_at', product_sku.created_at,
                          'updated_at', product_sku.updated_at
                        )
                      ) as skus
                    FROM product
                    LEFT JOIN product_sku ON product.product_id = product_sku.product_id
                    ${whereClause} 
                    GROUP BY product.product_id
                    ${sortClause}
                    LIMIT ${pageSize} OFFSET ${offset}
                `;

      const countSql = `
                    SELECT COUNT(DISTINCT product.product_id) AS total 
                    FROM product
                    LEFT JOIN product_sku ON product.product_id = product_sku.product_id
                    ${whereClause}
                `;

      const getListData = await ctx.service.admin.product.getProductList({ sql, countSql });

      if (getListData) {
        successRes({
          ctx,
          data: {
            ...getListData,
            current: Number(current),
            pageSize: Number(pageSize),
          },
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }

    } catch (error) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getProductDetails() {
    const { ctx } = this;
    const productId = ctx.params.id;

    try {
      const product = await ctx.service.admin.product.getProductById(productId);
      if (!product) {
        failRes({ ctx, message: '商品不存在' });
        return;
      }
      const productDetails = await ctx.service.admin.product.getProductDetailsById(productId);
      const skus = await ctx.service.admin.product.getProductSkusById(productId);

      const result = {
        ...productDetails,
        product_id: product.product_id,
        product_name: product.product_name,
        main_images: product.main_images ? JSON.parse(product.main_images) : [],
        details_image: productDetails.details_image ? JSON.parse(productDetails.details_image) : [],
        certificate_image: productDetails.certificate_image ? JSON.parse(productDetails.certificate_image) : [],
        code: product.code,
        category: product.category ? JSON.parse(product.category) : [],
        style: product.style,
        inlay: product.inlay,
        location: product.location,
        create_date: product.create_date,
        update_date: product.update_date,
        spec_lists: product.spec_lists ? JSON.parse(product.spec_lists) : [],
        skus: skus.map(sku => ({
          sku_id: sku.sku_id,
          sku_code: sku.sku_code,
          distribution_price: sku.distribution_price,
          stock_quantity: sku.stock_quantity,
          weight: sku.weight,
          size: sku.size,
          image: sku.image,
          state: sku.state,
          specs: sku.specs ? JSON.parse(sku.specs) : [],
          created_at: sku.created_at,
          updated_at: sku.updated_at,
        })),
      };

      successRes({
        ctx,
        data: result,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async addFavorite() {
    const { ctx } = this;
    try {
      ctx.validate({ product_id: 'number' }, ctx.request.body);
      const { product_id } = ctx.request.body;
      const customer_id = ctx.state.user_id; // 从JWT Token中获取用户ID

      // 检查商品是否存在
      const product = await ctx.service.admin.product.getProductById(product_id);
      if (!product) {
        failRes({ ctx, message: '商品不存在', code: 404 });
        return;
      }

      // 检查是否已经收藏
      const isFavorite = await ctx.service.customer.product.isFavorite(customer_id, product_id);
      if (isFavorite) {
        failRes({ ctx, message: '商品已收藏', code: 400 });
        return;
      }

      // 添加收藏
      await ctx.service.customer.product.addFavorite(customer_id, product_id);

      successRes({ ctx, message: '收藏成功' });
    } catch (err) {
      failRes({ ctx, message: '收藏失败' });
    }
  }

  async removeFavorite() {
    const { ctx } = this;
    try {
      ctx.validate({ product_id: 'number' }, ctx.request.body);
      const { product_id } = ctx.request.body;
      const customer_id = ctx.state.user_id; // 从JWT Token中获取用户ID

      // 检查是否已经收藏
      const isFavorite = await ctx.service.customer.product.isFavorite(customer_id, product_id);
      if (!isFavorite) {
        failRes({ ctx, message: '商品未收藏', code: 400 });
        return;
      }

      // 取消收藏
      await ctx.service.customer.product.removeFavorite(customer_id, product_id);

      successRes({ ctx, message: '取消收藏成功' });
    } catch (err) {
      failRes({ ctx, message: '取消收藏失败' });
    }
  }

  async checkFavorite() {
    const { ctx } = this;
    try {
      ctx.validate({ product_id: 'number' }, ctx.query);
      const { product_id } = ctx.query;
      const customer_id = ctx.state.user_id; // 从JWT Token中获取用户ID

      // 检查是否已经收藏
      const isFavorite = await ctx.service.customer.product.isFavorite(customer_id, Number(product_id));

      successRes({ ctx, data: { isFavorite } });
    } catch (err) {
      failRes({ ctx, message: '查询失败' });
    }
  }

  async getFavoriteList() {
    const { ctx } = this;
    try {
      ctx.validate({ current: 'number', pageSize: 'number' }, ctx.query);
      const { current = 0, pageSize = 0 } = ctx.query;
      const customer_id = ctx.state.user_id; // 从JWT Token中获取用户ID

      // 获取收藏列表
      const favoriteList = await ctx.service.customer.product.getFavoriteList(customer_id, Number(current), Number(pageSize));

      successRes({
        ctx,
        data: {
          ...favoriteList,
          current: Number(current),
          pageSize: Number(pageSize),
        },
      });
    } catch (err) {
      failRes({ ctx, message: '查询失败' });
    }
  }
}
