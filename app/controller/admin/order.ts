import { Controller } from 'egg';
import { successRes, failRes } from './../../utils/response';
import { SendQyMessage } from './../../utils/robot';
import { getDataChange, getDataChangeString } from './../../utils/logs';
export default class Order extends Controller {

  async createOrder() {
    const { ctx } = this;
    try {
      ctx.validate({ product_id: 'number', customer_id: 'number' }, ctx.request.body);
      const { product_id, user_id, shipping_address, recipient_name, recipient_phone, order_items, note, tracking_number, city, customer_id } = ctx.request.body;

      let order_id = ctx.helper.generateRandomNumericCode(18);
      while (await ctx.service.admin.order.isOrderIdExists(order_id)) {
        order_id = ctx.helper.generateRandomNumericCode(18);
      }

      await ctx.service.admin.order.createOrder({
        order_id: Number(order_id),
        product_id,
        user_id,
        shipping_address,
        recipient_name,
        recipient_phone,
        order_items,
        note,
        tracking_number,
        customer_id,
        operator_id: ctx.state.user_id,
        city: JSON.stringify(city || []),
      });

      successRes({
        ctx,
        data: null,
        isLog: true,
      });

    } catch (err) {
      failRes({ ctx, message: '非法参数' });
    }
  }

  async getOrderList() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, product_id, order_id, product_name, code, order_status, category, customer_id, customer_phone, user_id, operator_id, recipient_name, recipient_phone, tracking_number, created_at, orders } = ctx.query;
      const ordersStr = orders ? JSON.parse(orders) : {};
      const offset = (Number(current) - 1) * Number(pageSize);

      let whereClause = ' WHERE 1 = 1';

      if (product_id) {
        whereClause += ` AND order_data.product_id = ${product_id}`;
      }
      if (order_id) {
        whereClause += ` AND order_data.order_id = ${order_id}`;
      }
      if (user_id) {
        whereClause += ` AND order_data.user_id = ${user_id}`;
      }
      if (customer_id) {
        whereClause += ` AND order_data.customer_id = ${customer_id}`;
      }
      if (customer_phone) {
        whereClause += ` AND order_data.customer_phone = ${customer_phone}`;
      }
      if (operator_id) {
        whereClause += ` AND order_data.operator_id = ${operator_id}`;
      }
      if (recipient_name) {
        whereClause += ` AND order_data.recipient_name = ${recipient_name}`;
      }
      if (tracking_number) {
        whereClause += ` AND order_data.tracking_number = ${tracking_number}`;
      }
      if (recipient_phone) {
        whereClause += ` AND order_data.recipient_phone = ${recipient_phone}`;
      }
      if (created_at) {
        const date = JSON.parse(created_at);
        whereClause += ` AND order_data.created_at BETWEEN '${date[0]}' AND '${date[1]}'`;
      }
      if (order_status !== void 0) {
        whereClause += ` AND order_data.order_status IN ('${order_status}')`;
      }
      const sort = ordersStr && Object.keys(ordersStr).length > 0 ? {
        type: Object.keys(ordersStr)[0].includes('date') ? 'date' : 'other',
        name: Object.keys(ordersStr)[0],
        value: ordersStr[Object.keys(ordersStr)[0]] === 'ascend' ? 'ASC' : 'DESC',
      } : void 0;

      let sortClause = '';
      if (sort && sort.type === 'other') {
        sortClause += ` ORDER BY ${sort.name} ${sort.value}`;
      }

      if (sort && sort.type === 'date') {
        sortClause += ` ORDER BY ${sort.name} ${sort.value}`;
      }

      const sql = `
                  SELECT 
                      order_data.*,
                      CAST(order_id AS CHAR) AS order_id,
                      product.product_name,
                      product.main_images,
                      product.code,
                      product.category,
                      product.style,
                      product.inlay,
                      product.location,
                      customer_user.customer_name,
                      customer_user.phone as customer_phone,
                      customer_user.type as customer_type,
                      (
                          SELECT JSON_ARRAYAGG(
                              JSON_OBJECT(
                                  'sku_id', order_item.sku_id,
                                  'quantity', order_item.quantity,
                                  'unit_price', order_item.unit_price,
                                  'sku_info', (
                                    SELECT JSON_OBJECT(
                                        'specs', product_sku.specs
                                    )
                                    FROM product_sku
                                    WHERE product_sku.sku_id = order_item.sku_id
                                  )            
                              )
                          )
                          FROM order_item
                          WHERE order_item.order_id = order_data.order_id
                      ) AS order_items
                  FROM 
                      order_data
                  INNER JOIN 
                      product ON order_data.product_id = product.product_id
                  ${category ? ` AND JSON_CONTAINS(product.category, '${category}')` : ''}
                  ${product_name ? ` AND product.product_name LIKE '%${product_name}%'` : ''}
                  ${code ? ` AND product.code LIKE '%${code}%'` : ''}
                  INNER JOIN 
                      customer_user ON order_data.customer_id = customer_user.customer_id
                  ${whereClause} 
                  ${sortClause} 
                  LIMIT ${pageSize} OFFSET ${offset}
                  `;

      const countSql = `
                      SELECT COUNT(*) AS total 
                      FROM order_data
                      INNER JOIN product ON order_data.product_id = product.product_id 
                      ${category ? ` AND JSON_CONTAINS(product.category, '${category}')` : ''}
                      ${product_name ? ` AND product.product_name LIKE '%${product_name}%'` : ''}
                      ${code ? ` AND product.code LIKE '%${code}%'` : ''}
                      INNER JOIN customer_user ON order_data.customer_id = customer_user.customer_id
                      ${whereClause}
                      `;

      const orderListData = await ctx.service.admin.order.getOrderList({ sql, countSql });

      if (orderListData) {
        successRes({
          ctx,
          data: {
            ...orderListData,
            current: Number(current),
            pageSize: Number(pageSize),
          },
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async deleteOrder() {
    const { ctx } = this;
    try {
      const order_id = ctx.params.id;
      const order_data = await ctx.service.admin.order.getOneOrderData(order_id);
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id: Number(ctx.state.user_id) }, [ 'real_name', 'phone' ]);

      // 获取订单原始数据
      const originalOrder = await ctx.service.admin.order.getOrderById(order_id);
      if (!originalOrder) {
        failRes({ ctx, message: '订单不存在' });
        return;
      }

      const result = await ctx.service.admin.order.deleteOrder(order_id);

      for (const item of order_data) {
        await ctx.service.admin.product.updateProductInventory({
          type: 'add',
          num: item?.quantity || 0,
          sku_id: item?.sku_id,
        });
      }

      if (result) {
        const real_name = userinfo?.real_name + userinfo?.phone.substring(7, 11);
        SendQyMessage({
          ctx,
          data: {
            msgtype: 'markdown',
            markdown: {
              content: `${real_name}取消了订单 \n
                            > 订单id：<font color=\"comment\">${order_id}</font>
                            `,
            },
          },
        });

        // 记录操作日志
        await ctx.service.admin.logs.createOrderLogs({
          user_id: ctx.state.user_id,
          order_id,
          operation_type: '取消订单',
          operation_description: `取消订单order_id:${order_id}`,
        });

        successRes({ ctx, message: '取消成功' });
      } else {
        failRes({ ctx, message: '取消失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async updateOrder() {
    const { ctx } = this;
    try {
      ctx.validate({ order_id: 'string' }, ctx.request.body);
      const { shipping_address, recipient_name, recipient_phone, note, tracking_number, city, order_id, payment_status } = ctx.request.body;
      const getOriginalData = await ctx.service.admin.order.getOrderOriginalData(order_id);

      // 更新订单基本信息
      const result = await ctx.service.admin.order.updateOrder(order_id, {
        shipping_address,
        recipient_name,
        recipient_phone,
        note,
        tracking_number,
        city: JSON.stringify(city || []),
        payment_status,
      });
      if (result) {
        successRes({
          ctx,
          data: null,
          isLog: true,
        });
        if (getOriginalData) {
          const getNewData = await ctx.service.admin.order.getOrderOriginalData(order_id);
          const changes = getDataChange(getOriginalData?.[0], getNewData?.[0]);
          const changesStr = getDataChangeString(changes);
          if (Object.keys(changes).length > 0) {
            ctx.service.admin.logs.createOrderLogs({
              user_id: ctx.state.user_id,
              order_id,
              operation_type: '修改订单',
              operation_description: `修改订单信息order_id(${order_id})-` + changesStr,
            });
          }
        }
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '非法参数' });
    }
  }

  async updateOrderStatus() {
    const { ctx } = this;
    const { order_id, order_status } = ctx.request.body;
    ctx.validate({ order_id: 'string', order_status: 'string' }, ctx.request.body);
    try {

      // 获取订单原始数据
      const originalOrder = await ctx.service.admin.order.getOrderById(order_id);
      if (!originalOrder) {
        failRes({ ctx, message: '订单不存在' });
        return;
      }

      // 更新订单状态
      await ctx.service.admin.order.updateOrderStatus(order_id, order_status);

      // 获取更新后的订单数据
      const updatedOrder = await ctx.service.admin.order.getOrderById(order_id);

      // 计算变更记录
      const changes = getDataChange(originalOrder, updatedOrder);
      const changesStr = getDataChangeString(changes);

      // 记录操作日志
      if (Object.keys(changes).length > 0) {
        await ctx.service.admin.logs.createOrderLogs({
          user_id: ctx.state.user_id,
          order_id,
          operation_type: '修改订单状态',
          operation_description: `修改订单状态order_id(${order_id}):` + changesStr,
        });
      }

      successRes({ ctx });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getOrderDetails() {
    const { ctx } = this;
    const { id } = ctx.params;
    if (!id) {
      failRes({ ctx, message: '非法参数' });
      return;
    }
    try {
      const sql = `
      SELECT 
        order_data.*,
        CAST(order_id AS CHAR) AS order_id,
        product.product_name,
        product.main_images,
        product.code,
        product.category,
        product.style,
        product.inlay,
        product.state as product_state,
        product.location,
        customer_user.customer_name,
        customer_user.phone as customer_phone,
        customer_user.type as customer_type,
        (
          SELECT JSON_ARRAYAGG(
            JSON_OBJECT(
              'sku_id', order_item.sku_id,
              'quantity', order_item.quantity,
              'unit_price', order_item.unit_price,
              'sku_info', (
                SELECT JSON_OBJECT(
                   'sku_code', product_sku.sku_code,
                   'weight', product_sku.weight,
                   'size', product_sku.size,
                   'image', product_sku.image,
                   'state', product_sku.state,
                   'specs', product_sku.specs
                )
                FROM product_sku
                WHERE product_sku.sku_id = order_item.sku_id
              )            
            )
          )
          FROM order_item
          WHERE order_item.order_id = order_data.order_id
        ) AS order_items
      FROM 
        order_data
      INNER JOIN 
        product ON order_data.product_id = product.product_id
      INNER JOIN 
        customer_user ON order_data.customer_id = customer_user.customer_id
      WHERE order_data.order_id = ${id}
    `;

      const orderData = await ctx.service.admin.order.getOrderData(sql);

      if (orderData && orderData?.length > 0) {
        successRes({
          ctx,
          data: orderData[0],
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }

  }

}
