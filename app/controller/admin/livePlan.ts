import { Controller } from 'egg';
import { successRes, failRes } from './../../utils/response';

export default class LivePlanController extends Controller {

  async livePlanCreate() {
    const { ctx } = this;
    ctx.validate({ live_plan_name: 'string', live_start_time: 'string', live_end_time: 'string' }, ctx.request.body);
    const { live_plan_name, note, live_start_time, live_end_time } = ctx.request.body;
    try {
      await ctx.service.admin.livePlan.createLivePlan({
        live_plan_name,
        note,
        live_start_time,
        live_end_time,
        creator_id: ctx.state.user_id,
      });
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanUpdate() {
    const { ctx } = this;
    const { id } = ctx.params;
    ctx.validate({ live_plan_name: 'string', live_start_time: 'string', live_end_time: 'string' }, ctx.request.body);
    const { live_plan_name, note, live_start_time, live_end_time } = ctx.request.body;
    try {
      if (!id) {
        throw new Error('params error');
      }
      await ctx.service.admin.livePlan.updateLivePlan(id, {
        live_plan_name,
        note,
        live_start_time,
        live_end_time,
        creator_id: ctx.state.user_id,
      });
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanUpdateLock() {
    const { ctx } = this;
    const { is_locked, live_plan_id } = ctx.request.body;
    try {
      if (!live_plan_id) {
        throw new Error('params error');
      }
      await ctx.service.admin.livePlan.livePlanUpdateLock(live_plan_id, is_locked);
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanUpdateState() {
    const { ctx } = this;
    const { state, live_plan_id } = ctx.request.body;
    try {
      if (!live_plan_id || !state) {
        throw new Error('params error');
      }
      await ctx.service.admin.livePlan.livePlanUpdateState(live_plan_id, state);
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanDelete() {
    const { ctx } = this;
    const { id } = ctx.params;
    try {
      if (!id) {
        throw new Error('params error');
      }
      await ctx.service.admin.livePlan.deleteLivePlan(id);
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanList() {
    const { ctx } = this;
    const { live_plan_id, live_plan_name, live_start_time, live_end_time, state, user_id, created_at, is_locked, pageSize = 20, current = 1, orders } = ctx.query;
    try {
      const ordersStr = orders ? JSON.parse(orders) : {};

      let whereClause = ' WHERE 1 = 1';

      if (live_plan_id) {
        whereClause += ` AND lp.live_plan_id = ${live_plan_id}`;
      }
      if (live_plan_name) {
        whereClause += ` AND lp.live_plan_name LIKE '%${live_plan_name}%'`;
      }
      if (state) {
        whereClause += ` AND lp.state = '${state}'`;
      }
      if (is_locked) {
        whereClause += ` AND lp.is_locked = ${is_locked}`;
      }
      if (user_id) {
        whereClause += ` AND lp.creator_id = ${user_id}`;
      }
      if (live_start_time) {
        const date = JSON.parse(live_start_time);
        whereClause += ` AND lp.live_start_time BETWEEN '${date[0]}' AND '${date[1]}'`;
      }
      if (live_end_time) {
        const date = JSON.parse(live_end_time);
        whereClause += ` AND lp.live_end_time BETWEEN '${date[0]}' AND '${date[1]}'`;
      }
      if (created_at) {
        const date = JSON.parse(created_at);
        whereClause += ` AND lp.created_at BETWEEN '${date[0]}' AND '${date[1]}'`;
      }

      const sort = ordersStr && Object.keys(ordersStr).length > 0 ? {
        type: Object.keys(ordersStr)[0].includes('date') ? 'date' : 'other',
        name: Object.keys(ordersStr)[0],
        value: ordersStr[Object.keys(ordersStr)[0]] === 'ascend' ? 'ASC' : 'DESC',
      } : void 0;

      let sortClause = '';
      if (sort && sort.type === 'other') {
        sortClause += ` ORDER BY lp.${sort.name} ${sort.value}`;
      }

      if (sort && sort.type === 'date') {
        sortClause += ` ORDER BY lp.${sort.name} ${sort.value}`;
      }

      const offset = (Number(current) - 1) * Number(pageSize);

      const sql = `
                  SELECT
                        lp.*,
                        COUNT(lpd.product_id) AS product_count
                  FROM live_plan lp
                  LEFT JOIN
                  live_plan_detail lpd ON lp.live_plan_id = lpd.live_plan_id
                  ${whereClause}
                  GROUP BY
                  lp.live_plan_id
                  ${sortClause}
                  LIMIT ${pageSize} OFFSET ${offset}
                  `;

      const countSql = `
                       SELECT
                          COUNT(*) AS total
                       FROM
                          live_plan lp
                       LEFT JOIN
                          live_plan_detail lpd ON lp.live_plan_id = lpd.live_plan_id
                        ${whereClause}
                       `;
      const result = await ctx.service.admin.livePlan.livePlanList({ sql, countSql });
      if (result) {
        successRes({
          ctx,
          data: {
            ...result,
            current: Number(current),
            pageSize: Number(pageSize),
          },
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }

    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanAddProduct() {
    const { ctx } = this;
    const { live_plan_id, product_id } = ctx.request.body;
    try {
      if (!live_plan_id || !product_id || product_id?.length <= 0) {
        throw new Error('params error');
      }
      const livePlan = await ctx.service.admin.livePlan.getLivePlanById(live_plan_id);
      if (!livePlan) {
        failRes({ ctx, message: '计划不存在' });
        return;
      }
      if (livePlan.is_locked) {
        failRes({ ctx, message: '该计划已锁定' });
        return;
      }
      if (livePlan.state !== 'pending') {
        failRes({ ctx, message: `该计划状态为${livePlan.state === 'ended' ? '已结束' : '已取消'}` });
        return;
      }

      // 检查商品是否已经存在于货盘中
      const existingProducts = await ctx.service.admin.livePlan.getExistingProductsInLivePlan(live_plan_id, product_id);
      if (existingProducts && existingProducts.length > 0) {
        const existingProductIds = existingProducts.map((product: {id: number, live_plan_id: number, product_id: number}) => product.product_id);

        // 如果是单个商品且已存在，直接返回错误
        if (product_id.length === 1 && existingProductIds.includes(product_id[0])) {
          failRes({ ctx, message: `商品ID: ${product_id[0]} 已存在于该货盘中` });
          return;
        }

        // 如果是多个商品且部分已存在，返回不通过的商品列表
        if (existingProductIds.length > 0) {
          failRes({
            ctx,
            message: `以下商品已存在于该货盘中: ${existingProductIds.join(', ')}，共 ${existingProductIds.length} 个不通过`,
          });
          return;
        }
      }

      await ctx.service.admin.livePlan.livePlanAddProduct(live_plan_id, product_id || []);
      // 记录添加操作日志
      for (const product of product_id) {
        await ctx.service.admin.livePlan.createLivePlanDetailLogs({
          user_id: ctx.state.user_id,
          live_plan_id,
          live_plan_detail_id: product,
          operation_type: 'add',
          operation_description: `添加产品ID: ${product};`,
        });
      }
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanEnum() {
    const { ctx } = this;
    try {
      const sql = 'SELECT live_plan_id as value, live_plan_name as label FROM live_plan where state = "pending" ORDER BY created_at DESC LIMIT 100';

      const result = await ctx.service.admin.livePlan.select(sql);
      if (result) {
        successRes({
          ctx,
          data: result,
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }
    } catch (err) {
      console.error('livePlanEnum error:', err);
      failRes({ ctx, message: '未知错误' });
    }
  }


  async livePlanUpdateSortProduct() {
    const { ctx } = this;
    const sortItems = ctx.request.body;
    try {
      if (typeof sortItems !== 'object') {
        failRes({ ctx, message: '错误参数' });
        return;
      }
      await ctx.service.admin.livePlan.livePlanUpdateSortProduct(sortItems);
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanUpdateNoteProduct() {
    const { ctx } = this;
    const { id, note } = ctx.request.body;
    ctx.validate({ note: 'string', id: 'number' }, ctx.request.body);
    try {
      await ctx.service.admin.livePlan.livePlanUpdateProductInfo(id, { note });
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanUpdateConfirmedProduct() {
    const { ctx } = this;
    const { id, confirmed } = ctx.request.body;
    ctx.validate({ confirmed: 'number', id: 'number' }, ctx.request.body);
    try {
      const livePlanDetail = await ctx.service.admin.livePlan.getLivePlanDetailById(id);
      if (!livePlanDetail) {
        failRes({ ctx, message: '直播计划详情不存在' });
        return;
      }
      await ctx.service.admin.livePlan.livePlanUpdateProductInfo(id, { confirmed });
      // 记录更新操作日志
      await ctx.service.admin.livePlan.createLivePlanDetailLogs({
        user_id: ctx.state.user_id,
        live_plan_id: livePlanDetail?.live_plan_id,
        live_plan_detail_id: id,
        operation_type: 'update',
        operation_description: `更新计划ID: ${id} 的 confirmed 状态为 ${confirmed} ;产品id:${livePlanDetail?.product_id};`,
      });
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanUpdateIsLiveProduct() {
    const { ctx } = this;
    const { id, is_live } = ctx.request.body;
    ctx.validate({ is_live: 'number', id: 'number' }, ctx.request.body);
    try {
      const livePlanDetail = await ctx.service.admin.livePlan.getLivePlanDetailById(id);
      if (!livePlanDetail) {
        failRes({ ctx, message: '直播计划详情不存在' });
        return;
      }
      await ctx.service.admin.livePlan.livePlanUpdateProductInfo(id, { is_live });
      await ctx.service.admin.livePlan.createLivePlanDetailLogs({
        user_id: ctx.state.user_id,
        live_plan_id: livePlanDetail.live_plan_id,
        live_plan_detail_id: id,
        operation_type: 'update',
        operation_description: `更新计划ID: ${id} 的 is_live 状态为 ${is_live}  ;产品id:${livePlanDetail?.product_id};`,
      });
      successRes({
        ctx,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async livePlanDeleteProduct() {
    const { ctx } = this;
    const { live_plan_id, ids } = ctx.request.body;

    try {
      if (!ids || ids.length <= 0 || !live_plan_id) {
        throw new Error('params error');
      }
      const livePlan = await ctx.service.admin.livePlan.getLivePlanById(live_plan_id);
      if (!livePlan) {
        failRes({ ctx, message: '计划不存在' });
        return;
      }
      if (livePlan.is_locked) {
        failRes({ ctx, message: '该计划已锁定' });
        return;
      }
      if (livePlan.state !== 'pending') {
        failRes({ ctx, message: `该计划状态为${livePlan.state === 'ended' ? '已结束' : '已取消'}` });
        return;
      }
      const livePlanDetails = await ctx.service.admin.livePlan.getLivePlanDetailsByIds(ids);
      if (!livePlanDetails || livePlanDetails.length !== ids.length) {
        failRes({ ctx, message: '部分产品信息不存在' });
        return;
      }
      await ctx.service.admin.livePlan.livePlanDeleteProduct(live_plan_id, ids);
      // 记录删除操作日志
      for (const livePlanDetail of livePlanDetails) {
        await ctx.service.admin.livePlan.createLivePlanDetailLogs({
          user_id: ctx.state.user_id,
          live_plan_id,
          live_plan_detail_id: livePlanDetail?.id,
          operation_type: 'delete',
          operation_description: `删除计划ID: ${livePlanDetail.id} ;产品id:${livePlanDetail.product_id};`,
        });
      }
      successRes({ ctx });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getLivePlan() {
    const { ctx } = this;
    const { id } = ctx.params;

    try {
      if (!id) {
        throw new Error('params error');
      }
      const result = await ctx.service.admin.livePlan.getLivePlan(id);
      successRes({ ctx, data: result });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getLivePlanDetails() {
    const { ctx } = this;
    const { id } = ctx.params;

    try {
      if (!id) {
        throw new Error('params error');
      }
      const result = await ctx.service.admin.livePlan.getLivePlanDetails(id);
      successRes({ ctx, data: result });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

}
