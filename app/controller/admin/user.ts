import { Controller } from 'egg';
import { successRes, failRes } from './../../utils/response';
import { queryFormat } from './../../utils/query';
import argon2 from 'argon2';
import { decryptedText, hashPassword, generateRandomPassword } from './../../utils';
export default class user extends Controller {
  // admin
  async createAdminUser() {
    const { ctx } = this;
    try {
      ctx.validate({ real_name: 'string', username: 'string', phone: 'string' }, ctx.request.body);
      const { real_name, username, phone, email = '', avatar = '', user_role = 0 }:
      {
        username: string,
        real_name: string,
        phone: string,
        email: string,
        avatar?: string,
        user_role?: number,
      } = ctx.request.body;

      const temporaryPassword = generateRandomPassword();
      const getHashPassword = await hashPassword(temporaryPassword);
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id: ctx.state.user_id }, [ 'real_name' ]);
      const result = await ctx.service.admin.user.createAdminUser({
        real_name,
        username,
        phone,
        email,
        avatar,
        user_role,
        gender: 0,
        department: '',
        state: 0,
        password: getHashPassword,
        create_people: userinfo?.real_name || '',
        create_people_id: ctx.state.user_id || null,
      });
      if (result) {
        successRes({
          ctx,
          data: {
            password: temporaryPassword,
          },
        });
      } else {
        failRes({ ctx, message: '添加失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '添加失败' });
    }
  }

  async getAdminUserList() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, user_id, real_name, username, phone, email, state } = ctx.query;

      const condition = queryFormat({
        user_id: Number(user_id),
        real_name,
        username,
        phone,
        email,
        state: Number(state),
      });

      const userinfo = await ctx.service.admin.user.getUserInfoList({
        pageSize: Number(pageSize) > 1000 ? 1000 : Number(pageSize),
        current: Number(current),
        condition: condition ? ' WHERE ' + condition : '',
      });
      if (userinfo) {
        successRes({
          ctx,
          data: {
            ...userinfo,
            current: Number(current),
            pageSize: Number(pageSize),
          },
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getAdminUserProfilie() {
    const { ctx } = this;
    try {
      const { user_id } = ctx.params;
      if (!user_id) {
        failRes({ ctx, message: '非法参数' });
        return;
      }

      const userinfo = await ctx.service.admin.user.getUserProfilie(Number(user_id));
      if (userinfo) {
        successRes({
          ctx,
          data: userinfo,
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async updateAdminUserAvatar() {
    const { ctx } = this;
    try {
      ctx.validate({ avatar: 'string' }, ctx.request.body);
      const { avatar }: { avatar } = ctx.request.body;
      const result = await ctx.service.admin.user.updateAdminUser({ avatar }, Number(ctx.state.user_id));
      if (result) {
        successRes({ ctx });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '修改失败' });
    }
  }

  async updateAdminUser() {
    const { ctx } = this;
    try {
      ctx.validate({ real_name: 'string', username: 'string', phone: 'string', user_id: 'number' }, ctx.request.body);
      const { real_name, username, phone, email, user_id, user_role = 0 }:
      {
        username: string,
        real_name: string,
        phone: string,
        email: string,
        user_id?: string,
        user_role?: number,
      } = ctx.request.body;

      const result = await ctx.service.admin.user.updateAdminUser({
        real_name,
        username,
        phone,
        email,
        user_role,
      }, Number(user_id));

      if (result) {
        successRes({ ctx });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '修改失败' });
    }
  }

  async updateAdminUserState() {
    const { ctx } = this;
    try {
      ctx.validate({ user_id: 'number', state: 'number' }, ctx.request.body);
      const { user_id, state }: { user_id: number, state: number } = ctx.request.body;
      if (state > 3) {
        failRes({ ctx, message: '非法参数' });
        return;
      }
      const result = await ctx.service.admin.user.updateAdminUserState(Number(user_id), Number(state));
      if (result) {
        successRes({ ctx, message: '修改成功' });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '修改失败' });
    }
  }

  async updateAdminUserPassword() {
    const { ctx } = this;
    try {
      ctx.validate({ password: 'string', old_password: 'string' }, ctx.request.body);
      const { password, old_password }: { password: string, old_password: string } = ctx.request.body;
      if (!ctx?.state?.user_id || !password) {
        failRes({ ctx, message: '修改失败' });
        return;
      }
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id: ctx.state.user_id }, [ 'password' ]);
      const oldPassword = decryptedText(old_password);
      const isValidPassword = await argon2.verify(userinfo.password, oldPassword);

      if (!isValidPassword) {
        failRes({
          ctx,
          message: '旧密码错误',
          code: 400,
        });
        return;
      }

      const newPassword = decryptedText(password);
      const hashValue = await hashPassword(newPassword);
      const result = await ctx.service.admin.user.updateAdminUserPassword(ctx.state.user_id, hashValue);
      if (result) {
        successRes({ ctx });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '修改失败' });
    }
  }

  async resetAdminUserPassword() {
    const { ctx } = this;
    try {
      ctx.validate({ user_id: 'number' }, ctx.request.body);
      const { user_id }: { user_id: string } = ctx.request.body;
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id: Number(user_id) }, [ 'username', 'phone' ]);
      if (!userinfo) {
        failRes({ ctx, message: '用户不存在' });
        return;
      }
      const temporaryPassword = generateRandomPassword();
      const getHashPassword = await hashPassword(temporaryPassword);
      const result = await ctx.service.admin.user.updateAdminUserPassword(Number(user_id), getHashPassword);
      if (result) {
        successRes({ ctx, data: { password: temporaryPassword } });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '修改失败' });
    }
  }

  // customer
  async createCustomerUser() {
    const { ctx } = this;
    try {
      ctx.validate({ customer_name: 'string', username: 'string', phone: 'string', type: 'int' }, ctx.request.body);
      const { customer_name, username, phone, email = '', avatar = '', address = '', note = '', id_card_images = '', id_card_id = '', city, type }:
      {
        username: string,
        customer_name: string,
        phone: string,
        email: string,
        avatar?: string,
        address?: string,
        note?: string,
        id_card_images?: string,
        id_card_id?: string,
        city?: string,
        type: number,
      } = ctx.request.body;
      const temporaryPassword = generateRandomPassword();
      const getHashPassword = await hashPassword(temporaryPassword);
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id: ctx.state.user_id }, [ 'real_name' ]);
      const result = await ctx.service.admin.user.createCustomerUser({
        customer_name,
        username,
        address,
        note,
        phone,
        email,
        avatar,
        type: type ?? 0,
        state: 0,
        city: city ? JSON.stringify(city) : JSON.stringify(''),
        id_card_images: id_card_images ? JSON.stringify(id_card_images) : '',
        id_card_id,
        password: getHashPassword,
        create_people: userinfo?.real_name || '',
        create_people_id: ctx.state.user_id || null,
      });
      if (result) {
        successRes({
          ctx,
          data: {
            password: temporaryPassword,
          },
        });
      } else {
        failRes({ ctx, message: '添加失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '参数错误' });
    }
  }

  async updateCustomerUser() {
    const { ctx } = this;
    try {
      ctx.validate({ customer_name: 'string', username: 'string', phone: 'string', customer_id: 'number', type: 'int' }, ctx.request.body);
      const { customer_name, username, phone, email = '', avatar = '', address = '', note = '', customer_id, id_card_images = '', id_card_id = '', city, type }:
      {
        username: string,
        customer_name: string,
        phone: string,
        email: string,
        avatar?: string,
        address?: string,
        note?: string,
        city?: string,
        id_card_images?: string,
        id_card_id?: string,
        customer_id: number,
        type: number,
      } = ctx.request.body;
      if (!customer_id) {
        failRes({ ctx, message: '非法参数' });
        return;
      }
      const result = await ctx.service.admin.user.updateCustomerUser({
        customer_name,
        username,
        address,
        note,
        phone,
        email,
        city: city ? JSON.stringify(city) : JSON.stringify(''),
        avatar,
        id_card_id,
        type: type ?? 0,
        id_card_images: id_card_images ? JSON.stringify(id_card_images) : '',
      }, Number(customer_id));

      if (result) {
        successRes({ ctx });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '无效参数' });
    }
  }

  async getCustomerUserList() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, customer_id = '', customer_name = '', username = '', phone = '', type } = ctx.query;
      const condition = queryFormat({
        customer_id: Number(customer_id),
        customer_name,
        username,
        phone,
        type,
      });

      const customerUserList = await ctx.service.admin.user.getCustomerUserList({
        pageSize: Number(pageSize) > 1000 ? 1000 : Number(pageSize),
        current: Number(current),
        condition: condition ? ' WHERE ' + condition : '',
      });

      if (customerUserList) {
        successRes({
          ctx,
          data: {
            ...customerUserList,
            current: Number(current),
            pageSize: Number(pageSize),
          },
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async updateCustomerUserState() {
    const { ctx } = this;
    try {
      ctx.validate({ customer_id: 'number', state: 'number' }, ctx.request.body);
      const { customer_id, state }: { customer_id: number, state: number } = ctx.request.body;
      if (state > 3 || !customer_id) {
        failRes({ ctx, message: '非法参数' });
        return;
      }
      const result = await ctx.service.admin.user.updateCustomerUserState(Number(customer_id), Number(state));
      if (result) {
        successRes({ ctx, message: '修改成功' });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '修改失败' });
    }
  }

  async resetCustomerUserPassword() {
    const { ctx } = this;
    try {
      ctx.validate({ customer_id: 'number' }, ctx.request.body);
      const { customer_id }: { customer_id: string } = ctx.request.body;
      const supplierUserinfo = await ctx.service.admin.user.getCustomerUserInfo({ customer_id: Number(customer_id) }, [ 'username', 'phone' ]);
      if (!supplierUserinfo) {
        failRes({ ctx, message: '用户不存在' });
        return;
      }
      const temporaryPassword = generateRandomPassword();
      const getHashPassword = await hashPassword(temporaryPassword);
      const result = await ctx.service.admin.user.updateCustomerUserPassword(Number(customer_id), getHashPassword);
      if (result) {
        successRes({ ctx, data: { password: temporaryPassword } });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '无效参数' });
    }
  }

  // supplier
  async createSupplierUser() {
    const { ctx } = this;
    try {
      ctx.validate({ supplier_name: 'string', username: 'string', phone: 'string' }, ctx.request.body);
      const { supplier_name, username, phone, email = '', avatar = '', address = '', note = '', contacts = '' }:
      {
        username: string,
        supplier_name: string,
        phone: string,
        email: string,
        avatar?: string,
        address?: string,
        note?: string,
        contacts?: string
      } = ctx.request.body;
      const temporaryPassword = generateRandomPassword();
      const getHashPassword = await hashPassword(temporaryPassword);
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id: ctx.state.user_id }, [ 'real_name' ]);
      const result = await ctx.service.admin.user.createSupplierUser({
        supplier_name,
        username,
        address,
        note,
        phone,
        email,
        avatar,
        state: 0,
        contacts,
        password: getHashPassword,
        credentials_image: '',
        create_people: userinfo?.real_name || '',
        create_people_id: ctx.state.user_id || null,
      });
      if (result) {
        successRes({
          ctx,
          data: {
            password: temporaryPassword,
          },
        });
      } else {
        failRes({ ctx, message: '添加失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '添加失败' });
    }
  }

  async updateSupplierUser() {
    const { ctx } = this;
    try {
      ctx.validate({ supplier_name: 'string', username: 'string', phone: 'string', supplier_id: 'number' }, ctx.request.body);
      const { supplier_name, username, phone, email = '', avatar = '', address = '', note = '', contacts = '', supplier_id }:
      {
        username: string,
        supplier_name: string,
        phone: string,
        email: string,
        avatar?: string,
        address?: string,
        note?: string,
        contacts?: string,
        supplier_id?: number,
      } = ctx.request.body;
      if (!supplier_id) {
        failRes({ ctx, message: '非法参数' });
        return;
      }
      const result = await ctx.service.admin.user.updateSupplierUser({
        supplier_name,
        username,
        address,
        note,
        phone,
        email,
        avatar,
        contacts,
        credentials_image: '',
      }, Number(supplier_id));

      if (result) {
        successRes({ ctx });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '修改失败' });
    }
  }

  async getSupplierUserList() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, supplier_id = '', supplier_name = '', username = '', phone = '' } = ctx.query;
      const condition = queryFormat({
        supplier_id: Number(supplier_id),
        supplier_name,
        username,
        phone,
      });

      const supplierUserList = await ctx.service.admin.user.getSupplierUserList({
        pageSize: Number(pageSize) > 1000 ? 1000 : Number(pageSize),
        current: Number(current),
        condition: condition ? ' WHERE ' + condition : '',
      });

      if (supplierUserList) {
        successRes({
          ctx,
          data: {
            ...supplierUserList,
            current: Number(current),
            pageSize: Number(pageSize),
          },
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async resetSupplierUserPassword() {
    const { ctx } = this;
    try {
      ctx.validate({ supplier_id: 'number' }, ctx.request.body);
      const { supplier_id }: { supplier_id: string } = ctx.request.body;
      const supplierUserinfo = await ctx.service.admin.user.getSupplierUserInfo({ supplier_id: Number(supplier_id) }, [ 'username', 'phone' ]);
      if (!supplierUserinfo) {
        failRes({ ctx, message: '用户不存在' });
        return;
      }
      const temporaryPassword = generateRandomPassword();
      const getHashPassword = await hashPassword(temporaryPassword);
      const result = await ctx.service.admin.user.updateSupplierUserPassword(Number(supplier_id), getHashPassword);
      if (result) {
        successRes({ ctx, data: { password: temporaryPassword } });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '无效参数' });
    }
  }

  async updateSupplierUserState() {
    const { ctx } = this;
    try {
      ctx.validate({ supplier_id: 'number', state: 'number' }, ctx.request.body);
      const { supplier_id, state }: { supplier_id: number, state: number } = ctx.request.body;
      if (state > 3) {
        failRes({ ctx, message: '非法参数' });
        return;
      }
      const result = await ctx.service.admin.user.updateSupplierUserState(Number(supplier_id), Number(state));
      if (result) {
        successRes({ ctx, message: '修改成功' });
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '修改失败' });
    }
  }
}
