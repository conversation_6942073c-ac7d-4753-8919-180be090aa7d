import { Controller } from 'egg';
import { successRes, failRes } from './../../../utils/response';
import { DateTypeEnum, sqlDateFormat } from './../../../utils/query';

export default class orderData extends Controller {
  async getGmv() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'od.created_at',
        });
        const sql = `SELECT DATE(od.created_at) AS date,
                     SUM(oi.quantity * oi.unit_price) AS amount
                     FROM order_data od
                     INNER JOIN order_item oi ON od.order_id = oi.order_id
                     WHERE ${time}
                     GROUP BY DATE(od.created_at);
                    `;
        const res = await ctx.service.admin.data.getGmv({ sql });
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async salesVolume() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'od.created_at',
        });
        const sql = `
                     SELECT DATE(od.created_at) AS date,
                     SUM(oi.quantity) AS value
                     FROM order_data od
                     INNER JOIN order_item oi ON od.order_id = oi.order_id
                     WHERE ${time}
                     GROUP BY DATE(od.created_at);
                    `;
        const res = await ctx.service.admin.data.select({ sql });
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async orderVolume() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'od.created_at',
        });
        const sql = `
                     SELECT DATE(od.created_at) AS date,
                     COUNT(*) AS value
                     FROM order_data od
                     WHERE ${time}
                     GROUP BY DATE(od.created_at);
                    `;
        const res = await ctx.service.admin.data.select({ sql });
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getSingleProductTop() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'od.created_at',
        });
        const sql = `
                    SELECT od.product_id,
                    p.product_name,
                    COUNT(*) AS count
                    FROM order_data od
                    INNER JOIN product p ON od.product_id = p.product_id
                    WHERE ${time}
                    GROUP BY od.product_id, p.product_name
                    ORDER BY count DESC
                    LIMIT 30;
                    `;
        const res = await ctx.service.admin.data.select({ sql });
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getCategoryTop() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'od.created_at',
        });
        const sql = `
                    SELECT p.category,
                           SUM(oi.quantity) AS count
                    FROM order_data od
                    INNER JOIN order_item oi ON od.order_id = oi.order_id
                    INNER JOIN product p ON od.product_id = p.product_id
                    WHERE ${time}
                    GROUP BY p.category
                    ORDER BY count DESC;
                  `;
        const res = await ctx.service.admin.data.select({ sql });
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getCategoryGmvTop() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'od.created_at',
        });

        const sql = `
                    SELECT p.category,
                           SUM(oi.quantity * oi.unit_price) AS count
                    FROM order_data od
                    INNER JOIN order_item oi ON od.order_id = oi.order_id
                    INNER JOIN product p ON od.product_id = p.product_id
                    WHERE ${time}
                    GROUP BY p.category
                    ORDER BY count DESC;
                `;
        const res = await ctx.service.admin.data.select({ sql });
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getOrderAnalysis() {
    const { ctx } = this;
    try {
      const TotalAnalysis = await ctx.service.admin.data.getOrderAnalysis();

      if (TotalAnalysis) {
        successRes({ ctx, data: TotalAnalysis });
      } else {
        failRes({ ctx, message: '查询错误' });
      }

    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }
}
