import { Controller } from 'egg';
import { successRes, failRes } from './../../../utils/response';
import { sqlQueryFormat } from './../../../utils/query';

export default class userData extends Controller {
  async supplierUserDataEnum() {
    const { ctx } = this;
    try {
      const { sql } = sqlQueryFormat({
        params: [{
          type: 'state',
          name: 'state',
          value: '0',
          valueType: 'True',
        }],
        table_name: 'supplier_user',
        columns: [ 'supplier_id', 'supplier_name', 'contacts', 'phone' ],
      });

      const supplierUserDataList = await ctx.service.admin.data.getSupplierUserDataEnum({ sql });
      if (supplierUserDataList) {
        successRes({ ctx, data: supplierUserDataList });
      } else {
        failRes({ ctx, message: '查询错误' });
      }

    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async customerUserDataEnum() {
    const { ctx } = this;
    try {
      const { sql } = sqlQueryFormat({
        table_name: 'customer_user',
        columns: [ 'customer_id', 'customer_name', 'phone', 'type' ],
      });

      const customerUserDataList = await ctx.service.admin.data.getCustomerUserDataEnum({ sql });
      if (customerUserDataList) {
        successRes({ ctx, data: customerUserDataList });
      } else {
        failRes({ ctx, message: '查询错误' });
      }

    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async adminUserDataEnum() {
    const { ctx } = this;
    try {
      const { sql } = sqlQueryFormat({
        table_name: 'admin_user',
        columns: [ 'user_id', 'real_name' ],
      });

      const customerUserDataList = await ctx.service.admin.data.getAdminUserDataEnum({ sql });
      if (customerUserDataList) {
        successRes({ ctx, data: customerUserDataList });
      } else {
        failRes({ ctx, message: '查询错误' });
      }

    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getTotalAnalysis() {
    const { ctx } = this;
    try {
      const TotalAnalysis = await ctx.service.admin.data.getTotalAnalysis();

      if (TotalAnalysis) {
        successRes({ ctx, data: TotalAnalysis });
      } else {
        failRes({ ctx, message: '查询错误' });
      }

    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }
}
