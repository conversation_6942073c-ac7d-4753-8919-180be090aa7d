import { Controller } from 'egg';
import { successRes, failRes } from './../../../utils/response';
import { sqlDateTypeFormat, DateTypeEnum, sqlDateFormat } from './../../../utils/query';

export default class customData extends Controller {
  /** 客户增量趋势 */
  async getCustomIncrement() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const sql = sqlDateTypeFormat({
          total_name: '*',
          table_name: 'customer_user',
          time_name: 'create_date',
          total_type: 'COUNT',
          value_name: 'count',
          type: type as keyof DateTypeEnum,
          value,
        });
        const res = await ctx.service.admin.data.getGmv({ sql });
        if (res) {
          successRes({ ctx, data: res });
          return;
        }
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  /** 客户发展排行 */
  async getCustomIncrementTop() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'create_date',
        });
        const sql = `
                SELECT create_people_id, create_people, COUNT(*) AS count
                FROM customer_user
                WHERE ${time}
                GROUP BY create_people_id, create_people
                ORDER BY count DESC
                LIMIT 10;
                `;
        const res = await ctx.service.admin.data.select({ sql });
        if (res) {
          successRes({ ctx, data: res });
          return;
        }
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  /** 获取单王 */
  async getOrderTop() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'order_date',
        });
        const sql = `
                    SELECT order_data.customer_id as customer_id,customer_user.customer_name, COUNT(*) AS count
                    FROM order_data
                    INNER JOIN customer_user ON order_data.customer_id = customer_user.customer_id
                    WHERE ${time}
                    GROUP BY order_data.customer_id,customer_user.customer_name
                    ORDER BY count DESC
                    LIMIT 30;
                    `;
        const res = await ctx.service.admin.data.select({ sql });
        if (res) {
          successRes({ ctx, data: res });
          return;
        }
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  /** 获取客户消费排行 */
  async getConsumeBuyTop() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'od.created_at',
        });
        const sql = `
            SELECT 
                od.customer_id AS customer_id,
                cu.customer_name,
                SUM(oi.quantity * oi.unit_price) AS count
            FROM 
                order_data od
            INNER JOIN 
                order_item oi ON od.order_id = oi.order_id
            INNER JOIN 
                customer_user cu ON od.customer_id = cu.customer_id
            WHERE 
                ${time}
            GROUP BY 
                od.customer_id, cu.customer_name
            ORDER BY 
                count DESC
            LIMIT 30;
        `;
        const res = await ctx.service.admin.data.select({ sql });
        if (res) {
          successRes({ ctx, data: res });
          return;
        }
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  /** 获取城市分布 */
  async getLocationTop() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string' }, ctx.query);
      const { type } = ctx.query;

      if (type === 'province' || type === 'city') {
        const sql = `
                SELECT
                CASE   
                    WHEN city = '' THEN '0'   
                    ELSE city->>'$[${type === 'province' ? 0 : 1}]'
                END AS code,
                COUNT(*) AS count
                FROM customer_user
                GROUP BY code  
                ORDER BY count DESC;
                `;
        const res = await ctx.service.admin.data.select({ sql });
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }
}
