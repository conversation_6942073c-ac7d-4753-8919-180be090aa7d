import { Controller } from 'egg';
import { successRes, failRes } from './../../../utils/response';
import { sqlDateTypeFormat, DateTypeEnum, sqlDateFormat } from './../../../utils/query';

export default class productData extends Controller {
  async getProductEnterTop() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const time = sqlDateFormat({
          type: type as keyof DateTypeEnum,
          value,
          time_name: 'create_date',
        });
        const sql = `
                SELECT create_people_id, COUNT(*) AS count
                FROM product
                WHERE ${time}
                GROUP BY create_people_id
                ORDER BY count DESC
                LIMIT 10;
                `;
        const res = await ctx.service.admin.data.select({ sql });
        if (res) {
          successRes({ ctx, data: res });
          return;
        }
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getProductEnterTrend() {
    const { ctx } = this;
    try {
      ctx.validate({ type: 'string', value: 'string' }, ctx.query);
      const { type, value } = ctx.query;
      if (type && value) {
        const sql = sqlDateTypeFormat({
          total_name: '*',
          table_name: 'product',
          time_name: 'create_date',
          value_name: 'count',
          total_type: 'COUNT',
          type: type as keyof DateTypeEnum,
          value,
        });
        const res = await ctx.service.admin.data.select({ sql });
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getCategoryRatio() {
    const { ctx } = this;
    try {
      const sql = `
                SELECT category, COUNT(*) AS count
                FROM product
                GROUP BY category
                ORDER BY count DESC
                `;
      const res = await ctx.service.admin.data.select({ sql });
      if (res) {
        successRes({ ctx, data: res });
        return;
      }
      failRes({ ctx, data: '非法参数' });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }
}
