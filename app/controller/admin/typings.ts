export type CreateProductParam = {
  product_name: string;
  main_images?: string;
  category?: string[];
  style?: number;
  inlay?: number;
  open?: boolean;
  state: number;
  create_people_id?: number;
  supplier_id?: number;
  location?: number;
  spec_lists?: {
    spec_id: number,
    spec_name: string,
    spec_values: {
      spec_value_id: number,
      spec_value_name: string,
    }[]
  }[];
  description?: string;
  note?: string;
  details_image?: string;
  certificate_image?: string;
  skus?: {
    cost_price?: number;
    sales_price?: number;
    distribution_price?: number;
    stock_quantity: number;
    weight?: string;
    size?: string;
    image?: string;
    state?: 'active' | 'inactive';
    specs?: {
      spec_id: number,
      spec_name: string,
      spec_value_id: number,
      spec_value_name: string,
    }[]
  }[];
};

export interface GetProductListRes {
  product_id: number;
  product_name: string;
  images: string[];
  code: string;
  category: string[];
  style: number;
  inlay: number;
  open: boolean;
  state: number;
  create_date: string;
  create_people_id: number;
  supplier_id: number;
  spec_lists: {
    spec_id: number,
    spec_name: string,
    spec_values: {
      spec_value_id: number,
      spec_value_name: string,
    }[]
  }[] | [];
  update_date: string;
  skus: {
    sku_id: string;
    sku_code: string;
    cost_price: number;
    sales_price: number;
    distribution_price: number;
    stock_quantity: number;
    weight: string;
    size: string;
    image: string;
    state: 'active' | 'inactive';
    specs: {
      spec_id: number,
      spec_name: string,
      spec_value_id: number,
      spec_value_name: string,
    }[] | [],
    created_at: string;
    updated_at: string;
  }[];
}

