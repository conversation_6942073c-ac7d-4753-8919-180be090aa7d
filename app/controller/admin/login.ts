import { Controller } from 'egg';
import { WxSecretKey, tokenType } from './../../config/secret';
import { ONE_HOUR, ONE_DAY } from './../../config/const';
import { decryptedText } from './../../utils';
import { successRes, failRes } from './../../utils/response';
import argon2 from 'argon2';
import { v4 as uuidv4 } from 'uuid';
import jwt from 'jsonwebtoken';

export default class Login extends Controller {

  async getKey() {
    const { ctx } = this;
    successRes({
      ctx,
      data: { xkey: ctx.csrf },
    });
  }

  async adminLogin() {
    const { ctx } = this;
    try {
      ctx.validate({ username: 'string', password: 'string' }, ctx.request.body);
      const { username, password }: { username: string, password: string } = ctx.request.body;
      const usernameOrPhoneNumber = username.trim(); // 清除可能的空格

      // 获取客户端信息
      const ip = ctx.ip;
      const userAgent = ctx.get('user-agent');

      // 定义正则表达式匹配用户名和手机号码格式
      const phoneNumberRegex = /^\d{11}$/;

      let isPhoneNumber = false;

      // 检查是否符合手机号码格式
      if (phoneNumberRegex.test(usernameOrPhoneNumber)) {
        isPhoneNumber = true;
      } else {
        isPhoneNumber = false;
      }

      let userinfo: any;
      if (isPhoneNumber) {
        // 如果是手机号码
        userinfo = await ctx.service.admin.user.getUserInfo({ phone: Number(username) });
      } else {
        // 如果是用户名
        userinfo = await ctx.service.admin.user.getUserInfo({ username });
      }
      if (!userinfo) {
        failRes({
          ctx,
          message: '账户不存在',
          code: 401,
        });
        return;
      }

      const text = decryptedText(password);
      const isValidPassword = await argon2.verify(userinfo.password, text);

      if (!isValidPassword) {
        failRes({
          ctx,
          message: '账号或密码错误',
          code: 400,
        });
        return;
      }
      if (userinfo.state !== 0) {
        failRes({
          ctx,
          message: '账户异常,请联系管理员,code:' + userinfo.state,
          code: 401,
        });
        return;
      }
      const payload = {
        user_id: userinfo.user_id,
        token_type: tokenType.admin,
        exp: +new Date() + ONE_HOUR,
        iat: +new Date(),
        jti: uuidv4(),
      };
      const token = jwt.sign(payload, WxSecretKey);
      const refresh = jwt.sign(
        {
          ...payload,
          token_type: tokenType.admin + '_refresh',
          exp: +new Date() + ONE_DAY,
        },
        WxSecretKey,
      );

      // 记录登录成功日志
      await ctx.service.admin.logs.createLoginLogs({
        user_id: userinfo.user_id,
        username: userinfo.username,
        login_type: 'admin',
        ip,
        user_agent: userAgent,
        login_status: 'success',
      });

      successRes({
        ctx,
        data: {
          access: token,
          refresh,
        },
      });
      ctx.service.admin.user.updateUserInfoLastDate(userinfo.user_id);

    } catch (err: any) {
      failRes({ ctx, message: '登录失败，请稍后重试' });
    }
  }

  async getAdminUserProfilie() {
    const { ctx } = this;
    try {
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id: ctx.state.user_id });
      if (userinfo) {
        successRes({
          ctx,
          data: {
            user_id: userinfo?.user_id,
            username: userinfo?.username,
            avatar: userinfo?.avatar,
            real_name: userinfo?.real_name,
            phone: userinfo?.phone,
            user_role: userinfo?.user_role,
          },
        });
      } else {
        failRes({ ctx, data: null, message: '用户不存在', code: 401 });
      }
    } catch (err) {
      failRes({ ctx });
    }
  }

  async refreshAdminUserToken() {
    const { ctx } = this;
    try {
      ctx.validate({ rkey: 'string' }, ctx.request.body);
      const jwtData = jwt.verify(ctx.request.body.rkey, WxSecretKey);
      const { user_id, exp, token_type } = jwtData;
      if (!jwtData || !user_id || !token_type.includes('_refresh')) {
        failRes({ ctx, message: 'token error code : 2', code: 401 });
        return;
      }
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id }, [ 'state' ]);
      if (!userinfo) {
        failRes({ ctx, message: '用户不存在', code: 401 });
        return;
      }
      if (userinfo?.state !== 0) {
        failRes({ ctx, message: '账户异常,请联系管理员,code:' + (userinfo ? userinfo.state : ''), code: 401 });
        return;
      }
      if ((exp - +new Date()) > 0) {
        const payload = {
          user_id,
          token_type: tokenType.admin,
          exp: +new Date() + ONE_HOUR,
          iat: +new Date(),
          jti: uuidv4(),
        };
        const token = jwt.sign(payload, WxSecretKey);
        const refresh = jwt.sign(
          {
            ...payload,
            token_type: tokenType.admin + '_refresh',
            // exp: +new Date() + ONE_DAY 这样会永远不会退出
            exp,
          },
          WxSecretKey,
        );
        successRes({
          ctx,
          data: {
            access: token,
            refresh,
          },
        });
      } else {
        // 过期
        failRes({ ctx, message: 'token error code : 1', code: 401 });
      }
    } catch (err: any) {
      failRes({ ctx, code: 401, message: '登录过期' });
    }
  }
}
