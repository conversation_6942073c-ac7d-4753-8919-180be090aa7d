import { Controller } from 'egg';
import { successRes, failRes } from './../../utils/response';

export default class AdminBox extends Controller {
  async getBoxList() {
    const { ctx } = this;
    try {
      ctx.validate({ current: 'number', pageSize: 'number' }, ctx.query);
      const { current = 0, pageSize = 0, customer_id = '' } = ctx.query;

      const boxList = await ctx.service.admin.box.getBoxList(Number(current), Number(pageSize), {
        customer_id,
      });
      successRes({
        ctx,
        data: {
          ...boxList,
          current: Number(current),
          pageSize: Number(pageSize),
        },
      });
    } catch (err) {
      failRes({ ctx, message: '查询错误' });
    }
  }

  async insCustomerBox() {
    const { ctx } = this;
    try {
      ctx.validate({
        customer_id: 'string',
        box_name: 'string',
        box_quantity: 'number',
        image: 'string',
        warehouse: 'string',
      }, ctx.request.body);
      const { customer_id, box_name, box_quantity, image, warehouse } = ctx.request.body;

      // 执行添加盒子的业务逻辑
      const box_id = await ctx.service.admin.box.insCustomerBox({
        customer_id,
        box_name,
        box_quantity,
        image,
        warehouse,
      });

      if (box_id) {
        // 添加成功后记录日志
        await ctx.service.admin.box.insBoxLog({
          box_id,
          operation_type: 'add',
          quantity: box_quantity,
          operator: ctx.state.user_id,
          note: '新增盒子',
        });

        successRes({ ctx });
      } else {
        failRes({ ctx, message: '新增失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '新增失败' });
    }
  }

  async delCustomerBox() {
    const { ctx } = this;
    try {
      ctx.validate({
        box_id: 'string',
        note: { type: 'string', required: false },
      }, ctx.request.body);
      const { box_id, note } = ctx.request.body;

      // 获取盒子信息以记录库存
      const box = await ctx.service.admin.box.getBoxDetails(box_id);
      if (!box) {
        failRes({ ctx, message: '盒子不存在' });
        return;
      }

      // 执行删除盒子的业务逻辑
      const result = await ctx.service.admin.box.delCustomerBox(box_id);

      if (result) {
        // 删除成功后记录日志
        await ctx.service.admin.box.insBoxLog({
          box_id,
          operation_type: 'delete',
          quantity: box.box_quantity || 0,
          operator: ctx.state.user_id,
          note,
        });

        successRes({ ctx });
      } else {
        failRes({ ctx, message: '删除失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '删除失败' });
    }
  }

  async updateCustomerBoxInfo() {
    const { ctx } = this;
    try {
      ctx.validate({
        box_id: 'string',
        box_name: { type: 'string', required: false },
        image: { type: 'string', required: false },
        warehouse: { type: 'string', required: false },
        note: { type: 'string', required: false },
      }, ctx.request.body);

      const { box_id, box_name, image, warehouse, note } = ctx.request.body;

      // 检查盒子是否存在
      const box = await ctx.service.admin.box.getBoxDetails(box_id);
      if (!box) {
        failRes({ ctx, message: '盒子不存在' });
        return;
      }

      // 构建更新对象，只包含传入的字段
      const updateData: Record<string, any> = {};
      if (box_name !== undefined) updateData.box_name = box_name;
      if (image !== undefined) updateData.image = image;
      if (warehouse !== undefined) updateData.warehouse = warehouse;

      // 如果没有任何字段需要更新，直接返回成功
      if (Object.keys(updateData).length === 0) {
        successRes({ ctx });
        return;
      }

      // 执行更新盒子信息的业务逻辑
      const result = await ctx.service.admin.box.updateCustomerBoxInfo(box_id, updateData);

      if (result) {
        // 更新成功后记录日志
        await ctx.service.admin.box.insBoxLog({
          box_id,
          operation_type: 'update',
          quantity: 0, // 信息更新不涉及数量变化
          operator: ctx.state.user_id,
          note: note || `更新盒子信息：${Object.keys(updateData).join(',')}`,
        });

        successRes({ ctx });
      } else {
        failRes({ ctx, message: '更新失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '更新失败' });
    }
  }

  async updateCustomerBoxQuantity() {
    const { ctx } = this;
    try {
      ctx.validate({
        box_id: 'string',
        type: { type: 'enum', values: [ 'increase', 'decrease' ] },
        quantity: 'number',
        note: { type: 'string', required: false },
        order_id: { type: 'string', required: false },
      }, ctx.request.body);

      const { box_id, quantity, type, note, order_id } = ctx.request.body;

      // 执行更新库存的业务逻辑
      const result = await ctx.service.admin.box.updateCustomerBoxQuantity(box_id, quantity, type);

      if (!result.success) {
        failRes({ ctx, message: result.message || '更新失败' });
        return;
      }

      // 更新成功后记录日志
      await ctx.service.admin.box.insBoxLog({
        box_id,
        operation_type: type === 'increase' ? 'add' : 'reduce',
        quantity,
        operator: ctx.state.user_id,
        note,
        order_id,
      });

      successRes({ ctx });
    } catch (err) {
      failRes({ ctx, message: '更新失败' });
    }
  }

  async getBoxLogList() {
    const { ctx } = this;
    try {
      ctx.validate({
        box_id: 'string',
        current: 'number',
        pageSize: 'number',
      }, ctx.query);

      const { box_id, current = 1, pageSize = 20 } = ctx.query;

      const logList = await ctx.service.admin.box.getBoxLog(
        box_id,
        Number(current),
        Number(pageSize),
      );

      successRes({
        ctx,
        data: {
          ...logList,
          current: Number(current),
          pageSize: Number(pageSize),
        },
      });
    } catch (err) {
      failRes({ ctx, message: '查询日志失败' });
    }
  }
}
