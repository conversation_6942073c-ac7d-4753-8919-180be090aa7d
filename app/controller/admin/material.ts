import { Controller } from 'egg';
import { successRes, failRes } from './../../utils/response';

export default class MaterialController extends Controller {
  // 获取素材列表
  async list() {
    const { ctx } = this;
    const { product_id, current = '1', pageSize = '12', type = '', tag } = ctx.query;

    try {
      const result = await ctx.service.admin.material.listMaterials({
        product_id: Number(product_id),
        page: Number(current),
        pageSize: Number(pageSize),
        type,
        tag,
      });
      successRes({
        ctx,
        data: {
          ...result,
          current: Number(current),
          pageSize: Number(pageSize),
        },
      });
    } catch (error) {
      console.log(error, 'error');
      failRes({ ctx, message: '未知错误' });
    }
  }

  // 上传素材
  async create() {
    const { ctx } = this;
    const { product_id, name, type, url, tags } = ctx.request.body;

    try {
      const result = await ctx.service.admin.material.createMaterial({
        product_id,
        name,
        type,
        url,
        tags,
      });
      successRes({ ctx, data: result });
    } catch (error) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  // 更新素材
  async update() {
    const { ctx } = this;
    const { id } = ctx.params;
    const { name, tags, sort_order } = ctx.request.body;

    try {
      const result = await ctx.service.admin.material.updateMaterial(parseInt(id), {
        name,
        tags,
        sort_order,
      });
      successRes({ ctx, data: result });
    } catch (error) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  // 删除素材
  async delete() {
    const { ctx } = this;
    const { id } = ctx.params;

    try {
      const result = await ctx.service.admin.material.deleteMaterial(parseInt(id));
      successRes({ ctx, data: result });
    } catch (error) {
      failRes({ ctx, message: '未知错误' });
    }
  }
}
