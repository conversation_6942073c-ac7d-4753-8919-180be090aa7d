import { Controller } from 'egg';
import { sqlQueryFormat } from './../../utils/query';
import { successRes, failRes } from './../../utils/response';

export default class logs extends Controller {

  async getRequestLogs() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, request_id, method, url, user_id, code, created_at, id } = ctx.query;
      const { sql, countSql } = sqlQueryFormat({
        params: [{
          type: 'accurate',
          name: 'request_id',
          value: request_id,
        }, {
          type: 'accurate',
          name: 'method',
          value: method,
        }, {
          type: 'accurate',
          name: 'code',
          value: code,
        }, {
          type: 'like',
          name: 'url',
          value: url,
        }, {
          type: 'accurate',
          name: 'user_id',
          value: user_id,
        }, {
          type: 'accurate',
          name: 'id',
          value: id,
        }, {
          type: 'date_no_time',
          name: 'created_at',
          value: created_at,
        }],
        sort: {
          type: 'other',
          name: 'id',
          value: 'DESC',
        },
        pageSize: Number(pageSize),
        current: Number(current),
        table_name: 'request_logs',
      });
      const logs = await ctx.service.admin.logs.getRequestLogs(({ sql, countSql }));
      if (logs?.data) {
        successRes({
          ctx,
          data: {
            ...logs,
            current: Number(current),
            pageSize: Number(pageSize),
          },
          isLog: false,
        });
      } else {
        failRes({ ctx, message: '查询错误', isLog: false });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误', isLog: false });
    }
  }

  async getPorductChangeLogs() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, id, user_id, operation_type, created_at, product_id } = ctx.query;
      const { sql, countSql } = sqlQueryFormat({
        params: [{
          type: 'accurate',
          name: 'id',
          value: id,
        }, {
          type: 'accurate',
          name: 'user_id',
          value: user_id,
        }, {
          type: 'accurate',
          name: 'product_id',
          value: product_id,
        }, {
          type: 'accurate',
          name: 'operation_type',
          value: operation_type,
        }, {
          type: 'date_no_time',
          name: 'created_at',
          value: created_at,
        }],
        sort: {
          type: 'other',
          name: 'id',
          value: 'DESC',
        },
        pageSize: Number(pageSize),
        current: Number(current),
        table_name: 'product_logs',
      });

      const logs = await ctx.service.admin.logs.getRequestLogs(({ sql, countSql }));

      if (logs?.data) {
        successRes({
          ctx,
          data: {
            ...logs,
            current: Number(current),
            pageSize: Number(pageSize),
          },
          isLog: false,
        });
      } else {
        failRes({ ctx, message: '查询错误', isLog: false });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误', isLog: false });
    }
  }

  async getLivePlanDetailLogs() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, id, user_id, operation_type, created_at, live_plan_id, live_plan_detail_id } = ctx.query;
      const { sql, countSql } = sqlQueryFormat({
        params: [{
          type: 'accurate',
          name: 'id',
          value: id,
        }, {
          type: 'accurate',
          name: 'user_id',
          value: user_id,
        }, {
          type: 'accurate',
          name: 'live_plan_id',
          value: live_plan_id,
        }, {
          type: 'accurate',
          name: 'live_plan_detail_id',
          value: live_plan_detail_id,
        }, {
          type: 'accurate',
          name: 'operation_type',
          value: operation_type,
        }, {
          type: 'date_no_time',
          name: 'created_at',
          value: created_at,
        }],
        sort: {
          type: 'other',
          name: 'id',
          value: 'DESC',
        },
        pageSize: Number(pageSize),
        current: Number(current),
        table_name: 'live_plan_detail_log',
      });

      const logs = await ctx.service.admin.logs.getRequestLogs({ sql, countSql });

      if (logs?.data) {
        successRes({
          ctx,
          data: {
            ...logs,
            current: Number(current),
            pageSize: Number(pageSize),
          },
          isLog: false,
        });
      } else {
        failRes({ ctx, message: '查询错误', isLog: false });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误', isLog: false });
    }
  }

  async getOrderLogs() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, id, user_id, created_at, order_id } = ctx.query;
      const { sql, countSql } = sqlQueryFormat({
        params: [{
          type: 'accurate',
          name: 'id',
          value: id,
        }, {
          type: 'accurate',
          name: 'user_id',
          value: user_id,
        }, {
          type: 'accurate',
          name: 'order_id',
          value: order_id,
        }, {
          type: 'date_no_time',
          name: 'created_at',
          value: created_at,
        }],
        sort: {
          type: 'other',
          name: 'id',
          value: 'DESC',
        },
        pageSize: Number(pageSize),
        current: Number(current),
        table_name: 'order_logs',
      });

      const logs = await ctx.service.admin.logs.getRequestLogs(({ sql, countSql }));

      if (logs?.data) {
        successRes({
          ctx,
          data: {
            ...logs,
            current: Number(current),
            pageSize: Number(pageSize),
          },
          isLog: false,
        });
      } else {
        failRes({ ctx, message: '查询错误', isLog: false });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误', isLog: false });
    }
  }

  /**
   * 获取登录日志
   */
  async getLoginLogs() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, id, user_id, username, login_type, login_status, ip, day } = ctx.query;

      // 构建SQL和countSQL
      let sql = 'SELECT * FROM login_logs WHERE 1=1';
      let countSql = 'SELECT COUNT(*) as total FROM login_logs WHERE 1=1';
      const whereConditions: string[] = [];

      // 添加条件
      if (id) whereConditions.push(`id = ${id}`);
      if (user_id) whereConditions.push(`user_id = ${user_id}`);
      if (username) whereConditions.push(`username LIKE '%${username}%'`);
      if (login_type) whereConditions.push(`login_type = '${login_type}'`);
      if (login_status) whereConditions.push(`login_status = '${login_status}'`);
      if (ip) whereConditions.push(`ip LIKE '%${ip}%'`);

      // 处理时间筛选
      if (day) {
        // 按天筛选 - 精确到天
        whereConditions.push(`DATE(login_time) = '${day}'`);
      }

      // 添加WHERE条件
      if (whereConditions.length > 0) {
        const whereClause = whereConditions.join(' AND ');
        sql += ` AND ${whereClause}`;
        countSql += ` AND ${whereClause}`;
      }

      // 添加排序和分页
      sql += ' ORDER BY login_time DESC';
      sql += ` LIMIT ${Number(pageSize)} OFFSET ${Number(pageSize) * (Number(current) - 1)}`;


      const logs = await ctx.service.admin.logs.getLoginLogs({ sql, countSql });

      if (logs?.data) {
        successRes({
          ctx,
          data: {
            ...logs,
            current: Number(current),
            pageSize: Number(pageSize),
          },
          isLog: false,
        });
      } else {
        failRes({ ctx, message: '查询错误', isLog: false });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误', isLog: false });
    }
  }
}
