import { Controller } from 'egg';
import { successRes, failRes } from './../../utils/response';
import { formatChanges, compareSkus, compareObjects } from './../../utils/logs';
// import { getDataChange, getDataChangeString } from './../../utils/logs';

export default class Product extends Controller {
  async createProduct() {
    const { ctx } = this;
    ctx.validate({ product_name: 'string', category: 'object', style: 'number', supplier_id: 'number', inlay: 'number', main_images: 'object' }, ctx.request.body);
    const { product_name, main_images, category, style, inlay, open, state, location, supplier_id, spec_lists, description, note, details_image, certificate_image, product_source = 'D', skus, is_consignment } = ctx.request.body;
    try {
      let product_id = ctx.helper.generateRandomNumericCode(12);
      while (await ctx.service.admin.product.isProductExists(product_id)) {
        product_id = ctx.helper.generateRandomNumericCode(12);
      }

      let product_code = ctx.helper.generateRandomNumericCode(6);
      while (await ctx.service.admin.product.isCodeExists(product_source + product_code)) {
        product_code = ctx.helper.generateRandomNumericCode(6);
      }

      await ctx.service.admin.product.createProduct({
        product_id,
        product_name,
        main_images: JSON.stringify(main_images || []),
        code: product_source + product_code,
        category: JSON.stringify(category || []),
        style,
        inlay,
        open: open || 0,
        state,
        location,
        create_people_id: ctx.state.user_id || null,
        supplier_id,
        is_consignment: is_consignment || 0,
        spec_lists: JSON.stringify(spec_lists || []),
        product_source: product_source || 'D',
      });

      // 插入 product_details 表
      await ctx.service.admin.product.createProductDetails(product_id, {
        description,
        note,
        details_image: JSON.stringify(details_image || []),
        certificate_image: JSON.stringify(certificate_image || []),
      });

      // 插入 product_sku 表和记录价格历史
      for (const sku of skus) {
        let sku_code = ctx.helper.generateRandomNumericCode(8);
        while (await ctx.service.admin.product.isSkuCodeExists(product_source + sku_code)) {
          sku_code = ctx.helper.generateRandomNumericCode(8);
        }

        const skuResult = await ctx.service.admin.product.createProductSku(product_id, {
          sku_code: product_source + sku_code,
          ...sku,
          specs: JSON.stringify(sku?.space || []),
        });

        // 记录价格历史
        if (skuResult && skuResult.insertId) {
          await ctx.service.admin.product.recordProductPriceHistory({
            sku_id: skuResult.insertId,
            product_id,
            tag_price: sku.tag_price,
            cost_price: sku.cost_price,
            sales_price: sku.sales_price,
            distribution_price: sku.distribution_price,
            live_price: sku.live_price,
            operator_id: ctx.state.user_id,
            operation_type: 'create',
          });
        }
      }

      successRes({
        ctx,
        data: null,
        isLog: true,
      });

    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async updateProduct() {
    const { ctx } = this;
    const { id } = ctx.params;
    ctx.validate({ product_name: 'string', category: 'object', style: 'number', supplier_id: 'number', inlay: 'number', main_images: 'object' }, ctx.request.body);
    const { product_name, main_images, category, style, inlay, open, state, location, supplier_id, spec_lists,
      description, note, details_image, certificate_image, product_source = 'DS',
      skus,
    } = ctx.request.body;

    if (!id) {
      failRes({ ctx, message: '非法参数' });
      return;
    }

    try {
      // 获取更新前的商品数据
      const originalProduct = await ctx.service.admin.product.getProductById(id);
      const originalProductDetails = await ctx.service.admin.product.getProductDetailsById(id);
      const originalSkus = await ctx.service.admin.product.getProductSkus(id);


      // 更新 product 表
      await ctx.service.admin.product.updateProduct(id, {
        product_name,
        main_images: JSON.stringify(main_images || []),
        category: JSON.stringify(category || []),
        style,
        inlay,
        open: open || 0,
        state,
        location,
        supplier_id,
        product_source,
        spec_lists: JSON.stringify(spec_lists || []),
      });

      // 更新 product_details 表
      await ctx.service.admin.product.updateProductDetails(id, {
        description,
        note,
        details_image: JSON.stringify(details_image || []),
        certificate_image: JSON.stringify(certificate_image || []),
      });

      const updatedSkuIds: any[] = [];

      for (const sku of skus) {
        const { sku_id, cost_price, tag_price, sales_price, distribution_price, live_price, stock_quantity, weight, size, image, state, specs } = sku;
        if (sku_id) {
          // 检查价格是否有变化
          const originalSku = originalSkus.find(item => item.sku_id === sku_id);
          const priceChanged = originalSku && (
            originalSku.cost_price !== cost_price ||
            originalSku.sales_price !== sales_price ||
            originalSku.tag_price !== tag_price ||
            originalSku.distribution_price !== distribution_price ||
            originalSku.live_price !== live_price
          );

          // 更新SKU
          await ctx.service.admin.product.updateProductSku(sku_id, {
            cost_price,
            tag_price,
            sales_price,
            distribution_price,
            stock_quantity,
            live_price,
            weight,
            size,
            image,
            state,
            specs: JSON.stringify(specs || []),
          });

          // 如果价格有变化，记录价格历史
          if (priceChanged) {
            await ctx.service.admin.product.recordProductPriceHistory({
              sku_id,
              product_id: Number(id),
              cost_price,
              tag_price,
              sales_price,
              distribution_price,
              live_price,
              operator_id: ctx.state.user_id,
              operation_type: 'update',
            });
          }

          updatedSkuIds.push(sku_id);
        } else {
          // 插入 product_sku 表
          let sku_code = ctx.helper.generateRandomNumericCode(8);
          while (await ctx.service.admin.product.isSkuCodeExists((originalProduct?.product_source + 'D') + sku_code)) {
            sku_code = ctx.helper.generateRandomNumericCode(8);
          }

          const newSku: any = await ctx.service.admin.product.createProductSku(id, {
            sku_code: (originalProduct?.product_source || 'D') + sku_code,
            ...sku,
            specs: JSON.stringify(sku?.specs || []),
          });

          // 对新创建的SKU，始终记录价格历史，无需检查变化
          if (newSku && newSku.insertId) {
            await ctx.service.admin.product.recordProductPriceHistory({
              sku_id: newSku.insertId,
              product_id: Number(id),
              tag_price,
              cost_price,
              sales_price,
              distribution_price,
              live_price,
              operator_id: ctx.state.user_id,
              operation_type: 'create',
            });

            updatedSkuIds.push(newSku.insertId);
          } else if (newSku && newSku.sku_id) {
            updatedSkuIds.push(newSku.sku_id);
          }
        }
      }

      // 删除不再存在的 SKU
      const existingSkuIds = originalSkus.map(sku => sku.sku_id);
      const skusToDelete = existingSkuIds.filter(sku_id => !updatedSkuIds.includes(sku_id));

      for (const sku_id of skusToDelete) {
        await ctx.service.admin.product.deleteProductSku(sku_id);
      }

      // 获取更新后的商品数据
      const updatedProduct = await ctx.service.admin.product.getProductById(id);
      const updatedProductDetails = await ctx.service.admin.product.getProductDetailsById(id);
      const updatedSkus = await ctx.service.admin.product.getProductSkus(id);

      // 对比更新前后的数据
      const productChanges = compareObjects(originalProduct, updatedProduct);
      const productDetailsChanges = compareObjects(originalProductDetails, updatedProductDetails);
      const skuChanges = compareSkus(originalSkus, updatedSkus);

      // 生成操作日志
      const changesStr = formatChanges(productChanges, productDetailsChanges, skuChanges);
      if (changesStr) {
        await ctx.service.admin.logs.createUpdateProductLogs({
          user_id: ctx.state.user_id,
          product_id: id,
          operation_type: 0,
          operation_description: changesStr,
        });
      }

      successRes({
        ctx,
        data: null,
        isLog: true,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async deleteProduct() {
    const { ctx } = this;
    try {
      const product_id = Number(ctx.params.id);
      const result = await ctx.service.admin.product.deleteProduct(Number(product_id));
      if (result) {
        successRes({ ctx, message: '删除成功' });
        ctx.service.admin.logs.createUpdateProductLogs({
          user_id: ctx.state.user_id,
          product_id,
          operation_type: 1,
          operation_description: `删除商品:product_id:${product_id}`,
        });
      } else {
        failRes({ ctx, message: '删除失败', isLog: true });
      }
    } catch (err) {
      failRes({ ctx, message: '非法参数', isLog: true });
    }
  }

  async getProductList() {
    const { ctx } = this;
    const {
      pageSize = 20,
      current = 1,
      product_id,
      product_name,
      code,
      category,
      cost_price,
      sales_price,
      distribution_price,
      live_price,
      quantity,
      style,
      supplier_id,
      user_id,
      state,
      open,
      create_date,
      update_date,
      orders,
      is_consignment,
    }: {
      pageSize?: number;
      current?: number;
      product_id?: number,
      product_name?: string;
      code?: string;
      category?: string; // 产品类别
      cost_price?: string;
      sales_price?: string;
      distribution_price?: string,
      live_price?: string,
      style?: number; // 产品风格
      supplier_id?: number;
      user_id?: string;
      state?: number; // 产品状态
      open?: 0 | 1; // 是否开放
      quantity?: string,
      create_date?: string,
      update_date?: string,
      orders?: string,
      is_consignment?: 0 | 1, // 是否为代销产品
    } = ctx.query;

    try {
      const ordersStr = orders ? JSON.parse(orders) : {};

      let whereClause = ' WHERE 1 = 1';

      if (product_id) {
        whereClause += ` AND product.product_id LIKE '%${product_id}%'`;
      }
      if (product_name) {
        whereClause += ` AND product.product_name LIKE '%${product_name}%'`;
      }
      if (code) {
        whereClause += ` AND product.code LIKE '%${code}%'`;
      }
      if (user_id) {
        whereClause += ` AND product.create_people_id = ${user_id}`;
      }
      if (supplier_id) {
        whereClause += ` AND product.supplier_id = ${supplier_id}`;
      }
      if (open !== void 0) {
        whereClause += ` AND product.open = ${open}`;
      }
      if (is_consignment !== void 0) {
        whereClause += ` AND product.is_consignment = ${is_consignment}`;
      }
      if (style) {
        whereClause += ` AND product.style = ${style}`;
      }
      if (create_date) {
        const date = JSON.parse(create_date);
        whereClause += ` AND product.create_date BETWEEN '${date[0]}' AND '${date[1]}'`;
      }
      if (update_date) {
        const date = JSON.parse(update_date);
        whereClause += ` AND product.update_date BETWEEN '${date[0]}' AND '${date[1]}'`;
      }
      if (category) {
        whereClause += ` AND JSON_CONTAINS(product.category, '${category}')`;
      }
      if (cost_price) {
        const price = JSON.parse(cost_price);
        whereClause += ` AND product_sku.cost_price BETWEEN ${price?.[0] || 0} AND ${price?.[1] || 0}`;
      }
      if (sales_price) {
        const price = JSON.parse(sales_price);
        whereClause += ` AND product_sku.sales_price BETWEEN ${price?.[0] || 0} AND ${price?.[1] || 0}`;
      }
      if (distribution_price) {
        const price = JSON.parse(distribution_price);
        whereClause += ` AND product_sku.distribution_price BETWEEN ${price?.[0] || 0} AND ${price?.[1] || 0}`;
      }
      if (live_price) {
        const price = JSON.parse(live_price);
        whereClause += ` AND product_sku.live_price BETWEEN ${price?.[0] || 0} AND ${price?.[1] || 0}`;
      }
      if (quantity) {
        const quantity_num = JSON.parse(quantity);
        whereClause += ` AND product_sku.stock_quantity BETWEEN ${quantity_num?.[0] || 0} AND ${quantity_num?.[1] || 0}`;
      }
      if (state !== void 0) {
        whereClause += ` AND product.state = ${state}`;
      } else {
        whereClause += ' AND product.state != \'5\'';
      }

      const sort = ordersStr && Object.keys(ordersStr).length > 0 ? {
        type: Object.keys(ordersStr)[0].includes('date') ? 'date' : 'other',
        name: Object.keys(ordersStr)[0],
        value: ordersStr[Object.keys(ordersStr)[0]] === 'ascend' ? 'ASC' : 'DESC',
      } : void 0;

      let sortClause = '';
      if (sort && sort.type === 'other') {
        sortClause += ` ORDER BY ${sort.name} ${sort.value}`;
      }

      if (sort && sort.type === 'date') {
        sortClause += ` ORDER BY ${sort.name} ${sort.value}`;
      }

      const offset = (current - 1) * pageSize;

      const sql = `
            SELECT 
            product.product_id,
            ANY_VALUE(product.product_name) as product_name,
            ANY_VALUE(product.main_images) as main_images,
            ANY_VALUE(product.code) as code,
            ANY_VALUE(product.category) as category,
            ANY_VALUE(product.style) as style,
            ANY_VALUE(product.inlay) as inlay,
            ANY_VALUE(product.open) as open,
            ANY_VALUE(product.state) as state,
            ANY_VALUE(product.location) as location,
            ANY_VALUE(product.create_people_id) as create_people_id,
            ANY_VALUE(product.supplier_id) as supplier_id,
            ANY_VALUE(product.spec_lists) as spec_lists,
            ANY_VALUE(product.product_source) as product_source,
            ANY_VALUE(product.create_date) as create_date,
            ANY_VALUE(product.update_date) as update_date,
            ANY_VALUE(admin_user.real_name) as user_name,
            ANY_VALUE(supplier_user.supplier_name) as supplier_name,
            JSON_ARRAYAGG(
                JSON_OBJECT(
                     'sku_id', product_sku.sku_id,
                     'sku_code', product_sku.sku_code,
                     'cost_price', product_sku.cost_price,
                     'sales_price', product_sku.sales_price,
                     'tag_price', product_sku.tag_price,
                     'live_price', product_sku.live_price,
                     'distribution_price', product_sku.distribution_price,
                     'stock_quantity', product_sku.stock_quantity,
                     'weight', product_sku.weight,
                     'size', product_sku.size,
                     'image', product_sku.image,
                     'state', product_sku.state,
                     'specs', product_sku.specs,
                     'created_at', product_sku.created_at,
                     'updated_at', product_sku.updated_at
                  )
              ) as skus
            FROM product
            INNER JOIN admin_user ON product.create_people_id = admin_user.user_id
            INNER JOIN supplier_user ON product.supplier_id = supplier_user.supplier_id
            LEFT JOIN product_sku ON product.product_id = product_sku.product_id
            ${whereClause}
            GROUP BY product.product_id
            ${sortClause}
            LIMIT ${pageSize} OFFSET ${offset}
            `;

      const countSql = `
                SELECT COUNT(DISTINCT product.product_id) AS total
                FROM product
                INNER JOIN admin_user ON product.create_people_id = admin_user.user_id
                INNER JOIN supplier_user ON product.supplier_id = supplier_user.supplier_id
                LEFT JOIN product_sku ON product.product_id = product_sku.product_id
                ${whereClause}
            `;

      const orderListData = await ctx.service.admin.product.getProductList({ sql, countSql });

      if (orderListData) {
        successRes({
          ctx,
          data: {
            ...orderListData,
            current: Number(current),
            pageSize: Number(pageSize),
          },
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }

    } catch (error) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getProductSkus() {
    const { ctx } = this;
    const { id } = ctx.params;

    if (!id) {
      failRes({ ctx, message: '非法参数' });
      return;
    }

    try {
      const skus = await ctx.service.admin.product.getProductSkus(id);
      successRes({
        ctx,
        data: skus,
      });
    } catch (error) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getProductDetails() {
    const { ctx } = this;
    const productId = ctx.params.id;

    try {
      const product = await ctx.service.admin.product.getProductById(productId);
      if (!product) {
        failRes({ ctx, message: '商品不存在' });
        return;
      }
      const productDetails = await ctx.service.admin.product.getProductDetailsById(productId);
      const skus = await ctx.service.admin.product.getProductSkusById(productId);

      const result = {
        ...productDetails,
        product_id: product.product_id,
        product_name: product.product_name,
        main_images: product.main_images ? JSON.parse(product.main_images) : [],
        details_image: productDetails.details_image ? JSON.parse(productDetails.details_image) : [],
        certificate_image: productDetails.certificate_image ? JSON.parse(productDetails.certificate_image) : [],
        code: product.code,
        category: product.category ? JSON.parse(product.category) : [],
        style: product.style,
        inlay: product.inlay,
        open: product.open,
        state: product.state,
        location: product.location,
        product_source: product.product_source,
        create_date: product.create_date,
        create_people_id: product.create_people_id,
        supplier_id: product.supplier_id,
        is_consignment: product.is_consignment,
        spec_lists: product.spec_lists ? JSON.parse(product.spec_lists) : [],
        update_date: product.update_date,
        skus: skus.map(sku => ({
          sku_id: sku.sku_id,
          sku_code: sku.sku_code,
          tag_price: sku.tag_price,
          cost_price: sku.cost_price,
          sales_price: sku.sales_price,
          live_price: sku.live_price,
          distribution_price: sku.distribution_price,
          stock_quantity: sku.stock_quantity,
          weight: sku.weight,
          size: sku.size,
          image: sku.image,
          state: sku.state,
          specs: sku.specs ? JSON.parse(sku.specs) : [],
          created_at: sku.created_at,
          updated_at: sku.updated_at,
        })),
      };

      successRes({
        ctx,
        data: result,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getSpecList() {
    const { ctx } = this;
    const current = parseInt(ctx.query.page) || 1;
    const pageSize = parseInt(ctx.query.pageSize) || 20;
    try {
      const result = await ctx.service.admin.product.listSpecs(current, pageSize);
      successRes({
        ctx,
        data: result,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async createSpec() {
    const { ctx } = this;
    const { spec_name, values } = ctx.request.body;
    try {
      // 插入spec_name
      const result = await ctx.service.admin.product.createSpecName(spec_name);
      const specId = result.insertId;

      // 插入spec_value
      for (const value of values) {
        await ctx.service.admin.product.createSpecValue(specId, value.spec_value_name);
      }
      successRes({
        ctx,
        message: 'ok',
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async updateSpec() {
    const { ctx } = this;
    const { spec_id, spec_name, values } = ctx.request.body;
    try {
      // 更新spec_name
      await ctx.service.admin.product.updateSpecName(spec_id, spec_name);
      // 处理spec_value
      for (const value of values) {
        if (value.spec_value_id) {
          // 如果存在spec_value_id，则更新
          await ctx.service.admin.product.updateSpecValue(value.spec_value_id, value.spec_value_name);
        } else {
          // 如果不存在spec_value_id，则插入
          await ctx.service.admin.product.createSpecValue(spec_id, value.spec_value_name);
        }
      }
      successRes({
        ctx,
        message: 'ok',
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async deleteSpec() {
    const { ctx } = this;
    const { spec_id } = ctx.request.body;
    try {
      // 删除spec_value
      await ctx.service.admin.product.deleteSpecValues(spec_id);

      // 删除spec_name
      await ctx.service.admin.product.deleteSpecName(spec_id);

      successRes({
        ctx,
        message: 'ok',
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  // 获取商品价格历史记录
  async getProductPriceHistory() {
    const { ctx } = this;
    const product_id = ctx.params.id;

    try {
      if (!product_id) {
        failRes({ ctx, message: '缺少必要参数' });
        return;
      }

      const result = await ctx.service.admin.product.getProductPriceHistory(
        Number(product_id),
      );

      successRes({ ctx, data: result });
    } catch (err) {
      failRes({ ctx, message: '查询价格历史失败' });
    }
  }
}
