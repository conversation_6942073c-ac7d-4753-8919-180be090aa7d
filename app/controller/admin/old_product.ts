import { Controller } from 'egg';
import dayjs from 'dayjs';
import { successRes, failRes } from './../../utils/response';
import { sqlQueryFormat } from './../../utils/query';
import { getDataChange, getDataChangeString } from './../../utils/logs';
import type { CreateProductParam, UpdateProductParam } from './../../type/product';
export default class OldProduct extends Controller {
  async createProduct() {
    const { ctx } = this;
    try {
      ctx.validate({ product_name: 'string' }, ctx.request.body);
      const { product_name = '', images = [], size = '', category, style, mosaic, weight = '',
        place, cost_price, sales_price, distribution_price, sku, open, supplier_id,
        supplier_name, note, description, state, total_inventory }: CreateProductParam = ctx.request.body;
      const userinfo = await ctx.service.admin.user.getUserInfo({ user_id: ctx.state.user_id }, [ 'real_name' ]);
      const result = await ctx.service.admin.oldProduct.createProduct({
        product_name,
        images: JSON.stringify(images),
        size,
        category: JSON.stringify(category),
        style,
        mosaic,
        weight,
        place,
        cost_price,
        sales_price,
        distribution_price,
        open,
        supplier_id,
        supplier_name,
        note,
        state,
        description,
        total_inventory,
        create_people: userinfo?.real_name || '',
        create_people_id: ctx.state.user_id || null,
        update_date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        create_date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      });
      let createCode = '';
      if (category && style && supplier_id && result.insertId) {
        createCode = '' + category[1] + style + (Number(supplier_id) - 20000) + (Number(result.insertId) - 80000000);
      } else {
        failRes({ ctx, message: 'code生成失败' });
      }
      if (result.affectedRows === 1) {
        const resultCode = await ctx.service.admin.oldProduct.updateProduct({ code: createCode }, Number(result.insertId));
        let skuSql = 'INSERT INTO sku (sku_id, product_id, spec_name, spec_value ,inventory) VALUES';
        sku?.map((item, index) => {
          if (item.spec_name, item.spec_value) {
            skuSql += `('${Number(createCode + '' + (index + 1))}', '${Number(result.insertId)}', '${item.spec_name || ''}','${item.spec_value || ''}','${item.inventory || 0}')`;
            if (index !== sku.length - 1) {
              skuSql += ', ';
            }
          }
        });
        const resultSku = await ctx.service.admin.oldProduct.insertSku(skuSql);
        if (resultCode && resultSku.affectedRows === sku.length) {
          successRes({ ctx });
        } else {
          await ctx.service.admin.oldProduct.removeProduct(Number(result.insertId));
          failRes({ ctx, message: '添加失败' });
        }
      } else {
        failRes({ ctx, message: '添加失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '非法参数' });
    }
  }

  async deleteProduct() {
    const { ctx } = this;
    try {
      const product_id = Number(ctx.params.id);
      const result = await ctx.service.admin.oldProduct.deleteProduct(Number(product_id));
      if (result) {
        successRes({ ctx, message: '删除成功' });
        ctx.service.admin.logs.createUpdateProductLogs({
          user_id: ctx.state.user_id,
          product_id,
          operation_type: 1,
          operation_description: '删除商品:' + `product_id:${product_id};`,
        });
      } else {
        failRes({ ctx, message: '删除失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '非法参数' });
    }
  }

  async updateProduct() {
    const { ctx } = this;
    try {
      ctx.validate({ product_name: 'string', product_id: 'number' }, ctx.request.body);
      const { product_name = '', images = [], size = '', category, sku, style, mosaic, weight = '',
        place, cost_price, sales_price, distribution_price, total_inventory, open, supplier_id, product_id,
        supplier_name, note, description, state }: UpdateProductParam = ctx.request.body;
      let createCode = '';
      if (sku && sku?.length > 100) {
        failRes({ ctx, message: '非法sku参数' });
        return;
      }
      if (category && style && supplier_id && product_id) {
        createCode = '' + category[1] + style + (Number(supplier_id) - 20000) + (Number(product_id) - 80000000);
      } else {
        failRes({ ctx, message: 'code生成失败' });
      }
      const getOriginalProduct = await ctx.service.admin.oldProduct.getProductDetails(product_id);
      const result = await ctx.service.admin.oldProduct.updateProduct({
        product_name,
        images: JSON.stringify(images),
        code: createCode,
        size,
        category: JSON.stringify(category),
        style,
        mosaic,
        weight,
        place,
        cost_price,
        sales_price,
        distribution_price,
        open,
        supplier_id,
        supplier_name,
        note,
        state,
        description,
        total_inventory,
        update_date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      }, Number(product_id));
      const getDatabaseSku = await ctx.service.admin.oldProduct.getProductSku(Number(product_id));
      let skuChangeStr = '';
      sku?.map(async (item, index) => {
        if (!item.sku_id) {
          let sku_id = Number(createCode + '' + (index + 1));
          const flag = getDatabaseSku?.findIndex(findItem => findItem.sku_id === sku_id) > -1;
          if (flag) {
            sku_id += 1;
          }
          skuChangeStr += `新增sku(sku:${sku_id},product_id:${product_id},spec_name:${item.spec_name},spec_value:${item.spec_value},inventory${item.inventory});`;
          const skuSql = `INSERT INTO sku (sku_id, product_id, spec_name , spec_value ,inventory) VALUES  (${sku_id}, ${Number(product_id)}, '${item.spec_name}','${item.spec_value}',${item.inventory});`;
          await ctx.service.admin.oldProduct.insertSku(skuSql);
        }
        const sku_data = getDatabaseSku.find(findItem => findItem.sku_id === item.sku_id);
        if (sku_data) {
          if (item.spec_name !== sku_data.spec_name || item.spec_value !== sku_data.spec_value || item.inventory !== sku_data.inventory) {
            skuChangeStr += `更新sku(sku_id:${sku_data.sku_id},product_id:${product_id},`;
            for (const sku_key in item) {
              if (item[sku_key] !== sku_data[sku_key]) {
                skuChangeStr += `${sku_key}:${sku_data[sku_key]}=>${item[sku_key]},`;
              }
            }
            skuChangeStr += ');';
            await ctx.service.admin.oldProduct.updateSku({
              spec_name: item.spec_name,
              spec_value: item.spec_value,
              inventory: item.inventory,
            }, Number(item.sku_id));
          }
        }
      });
      getDatabaseSku?.map(async item => {
        const is_sku = sku?.filter(item => item.sku_id);
        const delete_sku = is_sku.find(findItem => findItem.sku_id === item.sku_id);
        if (!delete_sku) {
          skuChangeStr += `删除sku(sku_id:${item.sku_id},product_id:${product_id},spec_name:${item.spec_name},spec_value:${item.spec_value},inventory${item.inventory});`;
          await ctx.service.admin.oldProduct.deleteSku(Number(item.sku_id));
        }
      });

      if (result) {
        successRes({ ctx });
        const getNewProductData = await ctx.service.admin.oldProduct.getProductDetails(product_id);
        const changes = getDataChange(getOriginalProduct, getNewProductData);
        const changesStr = getDataChangeString(changes);
        if (Object.keys(changes).length > 0 || skuChangeStr) {
          ctx.service.admin.logs.createUpdateProductLogs({
            user_id: ctx.state.user_id,
            product_id,
            operation_type: 0,
            operation_description: `修改商品信息:product_id:${product_id}=>` + changesStr + skuChangeStr,
          });
        }
      } else {
        failRes({ ctx, message: '修改失败' });
      }
    } catch (err) {
      failRes({ ctx, message: '非法参数' });
    }
  }

  async updateProductState() {

  }

  async getProductList() {
    const { ctx } = this;
    try {
      const { pageSize = 20, current = 1, product_id, product_name, code, category, style, mosaic, open, state, supplier_id, orders, create_date, cost_price, sales_price, place } = ctx.query;
      const ordersStr = orders ? JSON.parse(orders) : {};
      const { sql, countSql } = sqlQueryFormat({
        params: [{
          type: 'like',
          name: 'product_id',
          value: product_id,
        }, {
          type: 'like',
          name: 'product_name',
          value: product_name,
        }, {
          type: 'like',
          name: 'code',
          value: code,
        }, {
          type: 'accurate',
          name: 'supplier_id',
          value: supplier_id,
        }, {
          type: 'json',
          name: 'category',
          value: category,
        }, {
          type: 'accurate',
          name: 'style',
          value: style,
        }, {
          type: 'accurate',
          name: 'mosaic',
          value: mosaic,
        }, {
          type: 'accurate',
          name: 'place',
          value: place,
        }, {
          type: 'accurate',
          name: 'open',
          value: open,
        }, {
          type: 'state',
          name: 'state',
          value: state,
          valueType: state !== void 0 ? 'True' : 'False',
        }, {
          type: 'range_data',
          name: 'create_date',
          value: create_date ? JSON.parse(create_date) : void 0 as any,
        }, {
          type: 'range_number',
          name: 'cost_price',
          value: cost_price ? JSON.parse(cost_price) : void 0 as any,
        }, {
          type: 'range_number',
          name: 'sales_price',
          value: sales_price ? JSON.parse(sales_price) : void 0 as any,
        }],
        pageSize: Number(pageSize),
        current: Number(current),
        table_name: 'old_product',
        sort: ordersStr && Object.keys(ordersStr).length > 0 ? {
          type: Object.keys(ordersStr)[0].includes('date') ? 'date' : 'other',
          name: Object.keys(ordersStr)[0],
          value: ordersStr[Object.keys(ordersStr)[0]] === 'ascend' ? 'ASC' : 'DESC',
        } : void 0,
      });

      const productListData = await ctx.service.admin.oldProduct.getProductList({ sql, countSql });

      if (productListData) {
        successRes({
          ctx,
          data: {
            ...productListData,
            current: Number(current),
            pageSize: Number(pageSize),
          },
        });
      } else {
        failRes({ ctx, message: '查询错误' });
      }
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getProductSku() {
    const { ctx } = this;
    const product_id = Number(ctx.params.id);
    if (!product_id) {
      failRes({ ctx, message: '非法参数' });
      return;
    }
    try {
      const product_sku = await ctx.service.admin.oldProduct.getProductSku(product_id);
      successRes({
        ctx,
        data: product_sku,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }

  async getProductDetails() {
    const { ctx } = this;
    const product_id = Number(ctx.params.id);
    if (!product_id) {
      failRes({ ctx, message: '非法参数' });
      return;
    }
    try {
      const product_details = await ctx.service.admin.oldProduct.getProductDetails(product_id);
      successRes({
        ctx,
        data: product_details,
      });
    } catch (err) {
      failRes({ ctx, message: '未知错误' });
    }
  }
}
