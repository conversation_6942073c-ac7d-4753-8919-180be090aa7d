import { Subscription } from 'egg';
import { SendQyMessage } from './../utils/robot';

export default class PendingOrderNotice extends Subscription {
  // 通过 schedule 属性来设置定时任务的执行间隔等配置
  static get schedule() {
    return {
      cron: '0 0 12,18 * * *', // 每天中午12点和晚上6点执行
      disable: true, // 暂时禁用此定时任务
      type: 'worker',
    };
  }

  // subscribe 是真正定时任务执行时被运行的函数
  async subscribe() {
    const { ctx } = this;

    const now = new Date();
    const hour = now?.getHours();

    // 晚上是 23 点到次日凌晨 8 点
    if (hour >= 23 || hour < 8) {
      // 晚上不执行任务
      return;
    }

    const totalPendingOrder = await ctx.service.admin.data.getPendingOrder();

    SendQyMessage({
      ctx,
      data: {
        msgtype: 'markdown',
        markdown: {
          content: `订单状态 \n
                    > 待处理订单:<font color=\"warning\">${totalPendingOrder || 0}</font>
                    `,
        },
      },
    });
  }
}
