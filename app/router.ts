/**
 * @param {Egg.Application} app - egg application
 */

import { Application } from 'egg';

export default (app: Application) => {
  const { router, controller: { index, admin, customer } } = app;

  // admin
  router.get('/api/admin/key', admin.login.getKey);
  router.post('/api/admin/login', admin.login.adminLogin);
  router.post('/api/admin/profilie', admin.login.getAdminUserProfilie);
  router.post('/api/admin/refresh', admin.login.refreshAdminUserToken);

  router.get('/api/admin/product/list', admin.product.getProductList);
  router.get('/api/admin/product/skus/:id', admin.product.getProductSkus);
  router.post('/api/admin/product/create', admin.product.createProduct);
  router.put('/api/admin/product/update/:id', admin.product.updateProduct);
  router.delete('/api/admin/product/delete/:id', admin.product.deleteProduct);
  router.get('/api/admin/product/details/:id', admin.product.getProductDetails);
  router.get('/api/admin/product/price/history/:id', admin.product.getProductPriceHistory);

  router.get('/api/admin/materials', admin.material.list); // 获取素材列表
  router.post('/api/admin/materials', admin.material.create); // 上传素材
  router.put('/api/admin/materials/:id', admin.material.update); // 更新素材
  router.delete('/api/admin/materials/:id', admin.material.delete); // 删除素材

  router.get('/api/admin/spec/list', admin.product.getSpecList);
  router.post('/api/admin/spec/create', admin.product.createSpec);
  router.post('/api/admin/spec/update', admin.product.updateSpec);
  router.post('/api/admin/spec/delete', admin.product.deleteSpec);

  // live plan
  router.post('/api/admin/live_plan/create', admin.livePlan.livePlanCreate);
  router.put('/api/admin/live_plan/update/:id', admin.livePlan.livePlanUpdate);
  router.put('/api/admin/live_plan/lock', admin.livePlan.livePlanUpdateLock);
  router.put('/api/admin/live_plan/state', admin.livePlan.livePlanUpdateState);
  router.delete('/api/admin/live_plan/delete/:id', admin.livePlan.livePlanDelete);
  router.get('/api/admin/live_plan/list', admin.livePlan.livePlanList);
  router.get('/api/admin/live_plan/detail/:id', admin.livePlan.getLivePlan);
  router.get('/api/admin/live_plan/enum', admin.livePlan.livePlanEnum);

  // live plan detail
  router.post('/api/admin/live_plan/add/product', admin.livePlan.livePlanAddProduct);
  router.put('/api/admin/live_plan/sort/product', admin.livePlan.livePlanUpdateSortProduct);
  router.put('/api/admin/live_plan/note/product', admin.livePlan.livePlanUpdateNoteProduct);
  router.put('/api/admin/live_plan/confirmed/product', admin.livePlan.livePlanUpdateConfirmedProduct);
  router.put('/api/admin/live_plan/is_live/product', admin.livePlan.livePlanUpdateIsLiveProduct);
  router.put('/api/admin/live_plan/delete/product', admin.livePlan.livePlanDeleteProduct);
  router.get('/api/admin/live_plan/detail/product/:id', admin.livePlan.getLivePlanDetails);

  router.post('/api/admin/old_product', admin.oldProduct.createProduct);
  router.put('/api/admin/old_product', admin.oldProduct.updateProduct);
  router.delete('/api/admin/old_product/delete/:id', admin.oldProduct.deleteProduct);
  router.get('/api/admin/old_product/details/:id', admin.oldProduct.getProductDetails);
  router.put('/api/admin/old_product/state', admin.oldProduct.updateProductState);
  router.get('/api/admin/old_product/list', admin.oldProduct.getProductList);
  router.get('/api/admin/old_product/sku/:id', admin.oldProduct.getProductSku);

  router.post('/api/admin/order', admin.order.createOrder);
  router.get('/api/admin/order/list', admin.order.getOrderList);
  router.delete('/api/admin/order/:id', admin.order.deleteOrder);
  router.put('/api/admin/order', admin.order.updateOrder);
  router.put('/api/admin/order/state', admin.order.updateOrderStatus);
  router.get('/api/admin/order/:id', admin.order.getOrderDetails);

  router.post('/api/admin/user', admin.user.createAdminUser);
  router.get('/api/admin/user/profilie/:user_id', admin.user.getAdminUserProfilie);
  router.get('/api/admin/user/list', admin.user.getAdminUserList);
  router.put('/api/admin/user', admin.user.updateAdminUser);
  router.put('/api/admin/user/state', admin.user.updateAdminUserState);
  router.put('/api/admin/user/password', admin.user.updateAdminUserPassword);
  router.put('/api/admin/user/avatar', admin.user.updateAdminUserAvatar);
  router.put('/api/admin/user/reset_password', admin.user.resetAdminUserPassword);

  router.post('/api/admin/customer', admin.user.createCustomerUser);
  router.put('/api/admin/customer', admin.user.updateCustomerUser);
  router.get('/api/admin/customer/list', admin.user.getCustomerUserList);
  router.put('/api/admin/customer/state', admin.user.updateCustomerUserState);
  router.put('/api/admin/customer/reset_password', admin.user.resetCustomerUserPassword);

  router.post('/api/admin/supplier', admin.user.createSupplierUser);
  router.put('/api/admin/supplier', admin.user.updateSupplierUser);
  router.put('/api/admin/supplier/state', admin.user.updateSupplierUserState);
  router.put('/api/admin/supplier/reset_password', admin.user.resetSupplierUserPassword);
  router.get('/api/admin/supplier/list', admin.user.getSupplierUserList);

  router.get('/api/admin/vip/list', admin.vip.getVipList);
  router.post('/api/admin/vip', admin.vip.createVipRule);
  router.put('/api/admin/vip', admin.vip.updateVipRule);
  router.delete('/api/admin/vip', admin.vip.deleteVipRule);

  // box
  router.get('/api/admin/box/list', admin.box.getBoxList);
  router.post('/api/admin/box', admin.box.insCustomerBox);
  router.put('/api/admin/box/update_quantity', admin.box.updateCustomerBoxQuantity);
  router.delete('/api/admin/box', admin.box.delCustomerBox);
  router.get('/api/admin/box/log', admin.box.getBoxLogList);
  router.put('/api/admin/box/update', admin.box.updateCustomerBoxInfo);

  router.get('/api/admin/data/supplier', admin.data.userData.supplierUserDataEnum);
  router.get('/api/admin/data/customer', admin.data.userData.customerUserDataEnum);
  router.get('/api/admin/data/admin', admin.data.userData.adminUserDataEnum);

  // total data
  router.get('/api/admin/data/gmv', admin.data.orderData.getGmv);
  router.get('/api/admin/data/salesVolume', admin.data.orderData.salesVolume);
  router.get('/api/admin/data/orderVolume', admin.data.orderData.orderVolume);
  router.get('/api/admin/data/productEnterTop', admin.data.productData.getProductEnterTop);
  router.get('/api/admin/data/productEnterTrend', admin.data.productData.getProductEnterTrend);
  router.get('/api/admin/data/customIncrement', admin.data.customData.getCustomIncrement);
  router.get('/api/admin/data/customIncrementTop', admin.data.customData.getCustomIncrementTop);
  router.get('/api/admin/data/orderTop', admin.data.customData.getOrderTop);
  router.get('/api/admin/data/consumeBuyTop', admin.data.customData.getConsumeBuyTop);
  router.get('/api/admin/data/customLocationTop', admin.data.customData.getLocationTop);
  router.get('/api/admin/data/singleProductTop', admin.data.orderData.getSingleProductTop);
  router.get('/api/admin/data/categoryTop', admin.data.orderData.getCategoryTop);
  router.get('/api/admin/data/categoryRatio', admin.data.productData.getCategoryRatio);
  router.get('/api/admin/data/categoryGmvTop', admin.data.orderData.getCategoryGmvTop);
  router.get('/api/admin/data/totalAnalysis', admin.data.userData.getTotalAnalysis);
  router.get('/api/admin/data/orderAnalysis', admin.data.orderData.getOrderAnalysis);

  // logs
  router.get('/api/admin/logs/request', admin.logs.getRequestLogs);
  router.get('/api/admin/logs/order', admin.logs.getOrderLogs);
  router.get('/api/admin/logs/product', admin.logs.getPorductChangeLogs);
  router.get('/api/admin/logs/live_plan', admin.logs.getLivePlanDetailLogs);
  router.get('/api/admin/logs/login', admin.logs.getLoginLogs);

  // customer
  router.get('/api/customer/key', customer.user.getKey);
  router.get('/api/customer/product/list', customer.product.getProductList);
  router.get('/api/customer/product/details/:id', customer.product.getProductDetails);

  router.get('/api/customer/order', customer.order.getOrderStatistics);
  router.get('/api/customer/order/list', customer.order.getOrderList);

  router.get('/api/customer/key', customer.user.getKey);
  router.post('/api/customer/login', customer.user.customerLogin);
  router.post('/api/customer/refresh', customer.user.refreshCustomerUserToken);
  router.get('/api/customer/profilie', customer.user.customerUserProfilie);
  router.put('/api/customer/password', customer.user.updatePassword);

  // customer favorite
  router.post('/api/customer/favorite/add', customer.product.addFavorite);
  router.post('/api/customer/favorite/remove', customer.product.removeFavorite);
  router.get('/api/customer/favorite/check', customer.product.checkFavorite);
  router.get('/api/customer/favorite/check', customer.product.checkFavorite);
  router.get('/api/customer/favorite/list', customer.product.checkFavorite);

  // customer box
  router.get('/api/customer/box/list', customer.box.getBoxList);
  router.get('/api/customer/box/details', customer.box.getBoxDetails);
  router.get('/api/customer/box/log', customer.box.getBoxLog);

  router.get('/', index.index);
};
