import { Context } from 'egg';
import { requestLogs } from '../utils/logs';

export default () => {
  return async function errorHandler(ctx: Context, next) {
    try {
      await next();
    } catch (err: any) {
      // 所有的异常都在 app 上触发一个 error 事件，框架会记录一条错误日志
      ctx.app.emit('error', err, ctx);
      ctx.status = 500;
      ctx.body = {
        code: 500,
        data: null,
        message: 'Internal Server Error',
      };
      requestLogs(ctx);
    }
  };
};
