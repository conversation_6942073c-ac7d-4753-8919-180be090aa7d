import { successRes } from './../utils/response';

const checkForSqlInjection = (input: string) => {
  const sqlInjectionPattern = /(?:--.*?(?:$|\r|\n)|#.*?(?:$|\r|\n)|(?<!\w)(?:\*|SELECT|UPDATE|DELETE|INSERT|DROP|TRUNCATE|EXEC|EXECUTE|GRANT|REVOKE|USE|SHOW|CREATE|ALTER|RENAME|DEFINE|CALL|DESCRIBE|EXPLAIN|HANDLER|LOAD|LOCK|REPLACE|SET|UNLOCK|RELEASE|SAVEPOINT|PREPARE|EXECUTE|DECLARE|MERGE|CURSOR|BULK|TEMP|TEMPORARY)(?!\w)|\b(?:UNION|JOIN|INTO|FROM|WHERE|GROUP\s+BY|HAVING|LIMIT|OFFSET|ORDER\s+BY|BY|ON|USING|IN|ALL|ANY|SOME|EXISTS|LEFT\s+JOIN|RIGHT\s+JOIN|FULL\s+JOIN|CROSS\s+JOIN|NATURAL\s+JOIN|INNER\s+JOIN|OUTER\s+JOIN|LEFT\s+OUTER\s+JOIN|RIGHT\s+OUTER\s+JOIN|CASE|WHEN|THEN|ELSE|END|AS|USING|IF|IFNULL|NULLIF|COALESCE|COLLATE)\b|;|\b(?:=|<|>|<=|>=|<>|!=|LIKE|ILIKE|REGEXP|SOUNDS LIKE|RLIKE)\b|\b(?:and|or|not|xor)\b|'(?:[^']|'')*'|"(?:[^"]|"")*")/i;

  if (input === '') {
    return false;
  }

  const matches = input?.toString()?.match(sqlInjectionPattern);
  return !!matches;
};

const queryFormat = (query: object) => {
  try {
    for (const key in query) {
      if (key === 'xkey') {
        return true;
      }
      if (key === 'current' || key === 'pageSize') {
        if (!query[key] || query[key] < 0) {
          return false;
        }
      }
      if (key === 'type' && query[key] === 'date') {
        return true;
      }
      switch (key) {
        case 'update_date':
        case 'cost_price':
        case 'create_date':
        case 'sales_price':
        case 'order_date':
        case 'distribution_price':
        case 'orders':
          if (typeof query[key] === 'number' || !Number.isNaN(Number(query[key]))) {
            return true;
          }
          if (typeof query[key] === 'string' && typeof JSON.parse(query[key]) !== 'object') {
            return false;
          }
          break;
        case 'value':
          if (checkForSqlInjection(query[key])) {
            return false;
          }
          return true;
        default:
          if (checkForSqlInjection(query[key])) {
            return false;
          }
          if (checkForSqlInjection(key)) {
            return false;
          }
          break;
      }
    }
    return true;
  } catch (err) {
    return false;
  }
};

const checkParams = (params: any) => {
  for (const key in params) {
    const value = params[key];
    if (typeof value !== 'number' && typeof value !== 'string') {
      return false;
    }
    if (checkForSqlInjection(value?.toString())) {
      return false;
    }
    if (checkForSqlInjection(key)) {
      return false;
    }
  }
  return true;
};

const checkArrayOfObjects = (array: unknown[]) => {
  for (const obj of array) {
    if (typeof obj !== 'object' || obj === null) {
      return false;
    }
    for (const key in obj) {
      const value = obj[key];
      if (typeof value !== 'number' && typeof value !== 'string') {
        return false;
      }
      if (checkForSqlInjection(value?.toString())) {
        return false;
      }
      if (checkForSqlInjection(key)) {
        return false;
      }
    }
  }
  return true;
};

export default () => {
  return async function paramCheck(ctx, next) {
    try {
      if (ctx.query && !queryFormat(ctx.query)) {
        throw new Error(`params error query url: ${ctx?.url} method:${ctx?.method}`);
      }

      if (ctx.params && !checkParams(ctx.params)) {
        throw new Error(`params error params url: ${ctx?.url} method:${ctx?.method}`);
      }

      if (ctx.request.body) {
        if (Array.isArray(ctx.request.body)) {
          if (!checkArrayOfObjects(ctx.request.body)) {
            throw new Error(`params error body url: ${ctx?.url} method:${ctx?.method}`);
          }
        } else if (!queryFormat(ctx.request.body)) {
          throw new Error(`params error body url: ${ctx?.url} method:${ctx?.method}`);
        }
      }

      await next();
    } catch (err) {
      console.log('paramCheck', err);
      successRes({ ctx, message: 'param ok' });
      return;
    }
  };
};
