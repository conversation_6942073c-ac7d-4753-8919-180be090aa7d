import { WxSecretKey } from './../config/secret';
import { failRes } from './../utils/response';
import jwt from 'jsonwebtoken';

export default () => {
  return async function jwtCheck(ctx, next) {
    const { url, header } = ctx.request;

    if (url === '/' || url?.includes('login') || url?.includes('refresh') || url?.includes('key')) {
      await next();
      return;
    }

    const token = header.authorization;
    if (!token) {
      failRes({ ctx, message: 'token error code : 4', code: 401 });
      return;
    }
    try {
      const jwtData = jwt.verify(token, WxSecretKey);
      const { token_type, user_id, exp } = jwtData;
      const tokenTypeMap = {
        admin: 'admin',
        customer: 'customer',
      };
      const flag = url?.includes(tokenTypeMap?.[token_type]) || false;
      if (!jwtData || !flag || !user_id) {
        failRes({ ctx, message: '登录过期，请重新登录', code: 401 });
        return;
      }
      // 过期时间减去当前时间如果大于0就是没有过期
      if ((exp - +new Date()) > 0) {
        ctx.state.user_id = user_id;
        await next();
      } else {
        // 过期
        failRes({ ctx, message: 'token error code : 1', code: 401 });
        return;
      }
    } catch (err) {
      // token解析失败
      failRes({ ctx, message: 'token error code : 3', code: 401 });
    }
  };
};
