import { Context, EggAppConfig } from 'egg';
import { v4 as uuidv4 } from 'uuid';
import { failRes } from './../utils/response';

interface RateLimitConfig {
  limit: number; // 每分钟允许的请求次数
  interval: number; // 时间间隔（毫秒）
}

export default (options: EggAppConfig['rateLimit']): any => {
  const { limit, interval } = options as RateLimitConfig;

  // 使用内存存储每个客户端的请求次数
  const requestCounts = new Map<string, { count: number; lastRequestTime: number }>();

  // 定时清理过期的请求计数器
  setInterval(() => {
    const now = Date.now();
    requestCounts.forEach((value, key) => {
      if (now - value.lastRequestTime > interval) {
        requestCounts.delete(key);
      }
    });
  }, interval);

  return async (ctx: Context, next: () => Promise<any>) => {
    const { url } = ctx;

    // 允许特定接口没有 clientId
    const allowWithoutClientId = url === '/api/admin/key';
    let clientId = ctx.cookies.get('clientId', { signed: false });

    if (!clientId && allowWithoutClientId) {
      clientId = uuidv4();
      ctx.cookies.set('clientId', clientId, { httpOnly: false, signed: false });
    }

    const now = Date.now();
    const clientRequest = requestCounts.get(clientId) || { count: 0, lastRequestTime: now };

    // 检查是否在时间间隔内
    if (now - clientRequest.lastRequestTime > interval) {
      clientRequest.count = 1;
      clientRequest.lastRequestTime = now;
    } else {
      clientRequest.count += 1;
    }

    // 更新请求次数
    requestCounts.set(clientId, clientRequest);

    // 检查是否超过限制
    if (clientRequest.count > limit) {
      failRes({ ctx, code: 429, message: '请求过于频繁，请稍后再试' });
      return;
    }

    await next();
  };
};
