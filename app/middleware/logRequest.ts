import { Context } from 'egg';
import { v4 as uuidv4 } from 'uuid';

export default () => {
  return async function logRequest(ctx: Context, next: () => Promise<any>) {

    // 生成唯一的 requestId
    const requestId = uuidv4();

    // 将 requestId 添加到响应头中
    ctx.set('X-Request-Id', requestId);

    // 将 requestId 添加到 ctx 中，以便在后续的中间件或控制器中使用
    ctx.requestId = requestId;

    // 记录请求开始时间
    const startTime = Date.now();

    // 等待下一个中间件或控制器处理请求
    await next();

    // 记录请求结束时间
    const endTime = Date.now();
    const duration = endTime - startTime;

    // 获取请求的详细信息
    const { method, url, headers, query, body, status } = ctx;

    // 构建日志信息
    const logMessage = {
      timestamp: new Date().toISOString(),
      requestId,
      method,
      url,
      headers,
      query,
      body,
      status,
      duration: `${duration}ms`,
    };

    // 将日志信息记录到日志文件中
    ctx.logger.info('Request Log:', logMessage);
  };
};
