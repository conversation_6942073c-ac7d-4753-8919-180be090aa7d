export type CreateOrderParam = {
    product_id: number,
    /**销售价格 */
    price: number,
    /**销售数量 */
    quantity: number,
    /**销售人id */
    user_id: string,
    /**客户id */
    customer_id: number,
    /**快递单号 */
    package_id?: string,
    /**收件人 */
    receiver_name?: string,
    city?: string | number[],
    /**收件人详细地址 */
    address?: string,
    /**收件人号码 */
    receiver_mobile?: string,
    /**备注 */
    note?: string,
    state?: number,
    is_insure?: number,
    insure_price?: number,
    sku_id: number,
}

export type UpdateOrderParam = CreateOrderParam & { order_id: number, return_id?: number };