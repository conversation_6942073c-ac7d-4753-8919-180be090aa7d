
export type SkuType = {
    id: number,
    sku_id?: number,
    product_id?: number,
    spec_name: string,
    spec_value: string,
    //库存
    inventory: number,
}

export type CreateProductParam = {
    product_name: string,
    images?: string[],
    //商品编码
    code?: string,
    //尺寸大小
    size?: string,
    //类别
    category: [number, number],
    /**款式 */
    style?: number,
    /**镶嵌 */
    mosaic?: number,
    /**重量 */
    weight?: string,
    /**产地 */
    place?: number,
    //成本价
    cost_price: number,
    //零售价
    sales_price?: number,
    //分销价
    distribution_price?: number,
    /**是否公开 */
    open?: 0 | 1,
    /**供应商id */
    supplier_id?: number,
    /**供应商 */
    supplier_name?: string,
    /**备注 */
    note?: string,
    /**商品描叙 */
    description?: string,
    state: number,
    total_inventory: number,
    sku: SkuType[],
}


export type UpdateProductParam = CreateProductParam & { product_id: number }