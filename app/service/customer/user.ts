import { Service } from 'egg';

export default class CustomerUser extends Service {
  async getUserInfoByUsername(username: string) {
    const { app } = this;
    const result = await app.mysql.get('customer_user', { username });
    return result;
  }

  async getUserInfoByPhone(phone: string) {
    const { app } = this;
    const result = await app.mysql.get('customer_user', { phone });
    return result;
  }

  async updateUserInfoLastDate(customer_id: number) {
    const { app } = this;
    const result = await app.mysql.update('customer_user', {
      customer_id,
      last_date: new Date(),
    });
    return result.affectedRows === 1;
  }

  async getUserInfoById(customer_id: number) {
    const { app } = this;
    const result = await app.mysql.get('customer_user', { customer_id });
    return result;
  }

  async updateUserPassword(customer_id: number, password: string) {
    const { app } = this;
    const result = await app.mysql.update('customer_user', {
      password,
    }, {
      where: { customer_id },
    });
    return result.affectedRows === 1;
  }
}
