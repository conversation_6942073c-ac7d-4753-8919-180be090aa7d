import { Service } from 'egg';

export default class CustomerBox extends Service {
  async getBoxList(customer_id: number, current: number, pageSize: number) {
    const { app } = this;
    const offset = (current - 1) * pageSize;
    const boxList = await app.mysql.select('customer_box', {
      where: { customer_id },
      limit: pageSize,
      offset,
    });
    return {
      total: await app.mysql.count('customer_box', { customer_id }),
      data: boxList,
    };
  }
  async getBoxDetails(box_id: number | string) {
    const { app } = this;
    const boxDetails = await app.mysql.get('customer_box', {
      box_id,
    });
    return boxDetails;
  }
  async getBoxLog(box_id:number | string, current: number, pageSize: number) {
    const { app } = this;
    const offset = (current - 1) * pageSize;
    const boxList = await app.mysql.select('customer_box_log', {
      where: { box_id },
      limit: pageSize,
      offset,
    });
    return {
      total: await app.mysql.count('customer_box_log', { box_id }),
      data: boxList,
    };
  }
}
