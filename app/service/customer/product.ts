import { Service } from 'egg';

export default class Product extends Service {
  async isFavorite(customer_id: number, product_id: number) {
    const { app } = this;
    const result = await app.mysql.get('customer_favorite', {
      customer_id,
      product_id,
    });
    return !!result;
  }

  async addFavorite(customer_id: number, product_id: number) {
    const { app } = this;
    const result = await app.mysql.insert('customer_favorite', {
      customer_id,
      product_id,
    });
    return result.affectedRows === 1;
  }

  async removeFavorite(customer_id: number, product_id: number) {
    const { app } = this;
    const result = await app.mysql.delete('customer_favorite', {
      customer_id,
      product_id,
    });
    return result.affectedRows === 1;
  }

  async getFavoriteList(customer_id: number, current: number, pageSize: number) {
    const { app } = this;
    const offset = (current - 1) * pageSize;

    // 获取收藏列表
    const favorites = await app.mysql.select('customer_favorite', {
      where: { customer_id },
      limit: pageSize,
      offset,
    });

    const productIds = favorites.map(favorite => favorite.product_id) || [];

    if (productIds.length === 0) {
      return {
        total: 0,
        list: [],
      };
    }

    // 获取商品详细信息
    const products = await app.mysql.query(
      `
      SELECT 
        product.product_id,
        product.product_name,
        product.main_images,
        product.code,
        product.category,
        product.style,
        product.inlay,
        product.location,
        product.create_date,
        product.update_date,
        product.spec_lists,
        JSON_ARRAYAGG(
          JSON_OBJECT(
            'sku_id', product_sku.sku_id,
            'sku_code', product_sku.sku_code,
            'distribution_price', product_sku.distribution_price,
            'stock_quantity', product_sku.stock_quantity,
            'weight', product_sku.weight,
            'size', product_sku.size,
            'image', product_sku.image,
            'state', product_sku.state,
            'specs', product_sku.specs,
            'created_at', product_sku.created_at,
            'updated_at', product_sku.updated_at
          )
        ) as skus
      FROM product
      LEFT JOIN product_sku ON product.product_id = product_sku.product_id
      WHERE product.product_id IN (?)
      GROUP BY product.product_id
      `,
      [ productIds ],
    );

    return {
      total: await app.mysql.count('customer_favorite', { customer_id }),
      data: products,
    };
  }
}
