import { Service } from 'egg';

export default class CustomerOrder extends Service {
  async getOrderList(customer_id: number, current: number, pageSize: number, params: {
    time_filter?: { start_time: string; end_time: string };
    order_status?: string;
  }) {
    const { app } = this;
    const offset = (current - 1) * pageSize;

    // 构建查询条件
    const where: any = { customer_id };
    if (params.order_status) {
      where.order_status = params.order_status;
    }

    // 处理时间范围查询
    const queryOptions: any = {
      where,
      limit: pageSize,
      offset,
    };

    // 如果有时间筛选，使用BETWEEN查询
    if (params.time_filter && params.time_filter.start_time && params.time_filter.end_time) {
      // 使用原生SQL进行时间范围查询
      const sql = `
        SELECT * FROM order_data 
        WHERE customer_id = ? 
        ${params.order_status ? 'AND order_status = ?' : ''} 
        AND created_at BETWEEN ? AND ?
        LIMIT ? OFFSET ?
      `;

      const values = [
        customer_id,
        ...(params.order_status ? [ params.order_status ] : []),
        params.time_filter.start_time,
        params.time_filter.end_time,
        pageSize,
        offset,
      ];

      const orderList = await app.mysql.query(sql, values);

      // 计算总数
      const countSql = `
        SELECT COUNT(*) as total FROM order_data 
        WHERE customer_id = ? 
        ${params.order_status ? 'AND order_status = ?' : ''} 
        AND created_at BETWEEN ? AND ?
      `;

      const countValues = [
        customer_id,
        ...(params.order_status ? [ params.order_status ] : []),
        params.time_filter.start_time,
        params.time_filter.end_time,
      ];

      const totalResult = await app.mysql.query(countSql, countValues);
      const total = totalResult[0]?.total || 0;

      return {
        total,
        data: orderList,
        current,
        pageSize,
      };
    }

    // 没有时间筛选，使用普通查询
    const orderList = await app.mysql.select('order_data', queryOptions);

    return {
      total: await app.mysql.count('order_data', where),
      data: orderList,
      current,
      pageSize,
    };
  }

  async getOrderStatistics(customer_id: number) {
    const { app } = this;
    // 使用原生 SQL 查询，避免类型错误
    const sql = `
      SELECT order_status, COUNT(*) as count 
      FROM order_data 
      WHERE customer_id = ? 
      GROUP BY order_status
    `;
    const orderStatistics = await app.mysql.query(sql, [ customer_id ]);
    return orderStatistics;
  }
}
