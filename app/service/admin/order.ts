import { Service } from 'egg';
export default class Order extends Service {

  async isOrderIdExists(order_id) {
    const result = await this.app.mysql.select('order_data', {
      where: { order_id },
      columns: [ 'order_id' ],
    });
    return result.length > 0;
  }

  async createOrder({
    order_id,
        product_id, user_id, shipping_address, recipient_name,
        recipient_phone, order_items, note, tracking_number,
        city, customer_id, operator_id,
  }: {
    order_id: number,
    product_id: number, user_id: number, shipping_address: string,
    recipient_name: string, recipient_phone: number,
    tracking_number: number, city: string,
    note: string, order_items: any[], customer_id: number, operator_id: number
  }) {
    const { app } = this;

    await app.mysql.insert('order_data', {
      order_id,
      product_id,
      user_id,
      customer_id,
      note,
      shipping_address,
      tracking_number,
      recipient_name,
      recipient_phone,
      operator_id,
      city,
    });

    for (const item of order_items) {
      const sku = await app.mysql.get('product_sku', { sku_id: item.sku_id });
      if (!sku) {
        throw new Error(`SKU with ID ${item.sku_id} not found`);
      }
      const unit_price = sku.distribution_price || 0; // 使用销售价格或成本价格
      await app.mysql.insert('order_item', {
        order_id,
        sku_id: item.sku_id,
        quantity: item.quantity,
        unit_price,
      });

      // 减库存
      const newStockQuantity = sku.stock_quantity - item.quantity;
      if (newStockQuantity < 0) {
        throw new Error(`Not enough stock for SKU with ID ${item.sku_id}`);
      }
      const sql = `UPDATE product_sku
                   SET stock_quantity = stock_quantity - ${item.quantity}
                   WHERE sku_id = ${item.sku_id}`;
      await this.app.mysql.query(sql);
    }

    return order_id;
  }

  async getOrderList({ sql, countSql }: { sql: string, countSql: string }) {
    const { app } = this;
    const data = await app.mysql.query(sql);
    const count = await app.mysql.query(countSql);
    const total = count[0].total;
    return {
      data,
      total,
    };
  }

  async deleteOrder(order_id: number) {
    const sql = `UPDATE order_data SET order_status = 'cancelled' WHERE order_id = ${order_id}`;
    const result = await this.app.mysql.query(sql);
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async getOrderData(sql: string) {
    const { app } = this;
    const data = await app.mysql.query(sql);
    return data;
  }

  async getOneOrderData(order_id: number) {
    const { app } = this;
    const data = await app.mysql.select('order_item', {
      columns: [ 'quantity', 'sku_id' ],
      where: {
        order_id,
      },
    });
    return data;
  }

  async updateOrder(order_id: string, param: any) {
    const result = await this.app.mysql.update('order_data', param, {
      where: {
        order_id,
      },
    });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async getOrderById(order_id) {
    const result = await this.app.mysql.get('order_data', { order_id });
    return result;
  }

  async updateOrderStatus(order_id, order_status) {
    const result = await this.app.mysql.update('order_data', {
      order_status,
    }, {
      where: {
        order_id,
      },
    });
    return result;
  }

  async getOrderOriginalData(order_id: number) {
    const { app } = this;
    const data = await app.mysql.select('order_data', {
      where: {
        order_id,
      },
    });
    return data;
  }
}
