// app/controller/livePlanDetail.ts
import { Controller } from 'egg';

export default class LivePlanDetailController extends Controller {
  async createLivePlan(data) {
    return await this.app.mysql.insert('live_plan', data);
  }

  async updateLivePlan(id: number, data) {
    return await this.app.mysql.update('live_plan', { ...data }, { where: { live_plan_id: id } });
  }

  async livePlanUpdateLock(id: number, is_locked: boolean) {
    return await this.app.mysql.update('live_plan', { is_locked }, { where: { live_plan_id: id } });
  }

  async livePlanUpdateState(id: number, state) {
    return await this.app.mysql.update('live_plan', { state }, { where: { live_plan_id: id } });
  }

  async deleteLivePlan(id: number) {
    return await this.app.mysql.update('live_plan', { state: 'cancelled' }, { where: { live_plan_id: id } });
  }

  async select(sql: string) {
    return await this.app.mysql.query(sql);
  }

  async livePlanList({ sql, countSql }: { sql: string, countSql: string }) {
    const { app } = this;
    const data = await app.mysql.query(sql);
    const count = await app.mysql.query(countSql);
    const total = count[0].total;
    return {
      data,
      total,
    };
  }

  async getLivePlanById(live_plan_id: number) {
    const result = await this.app.mysql.get('live_plan', { live_plan_id });
    return result;
  }

  async getLivePlanDetailsByIds(ids) {
    const results = await this.app.mysql.select('live_plan_detail', {
      where: { id: ids },
      columns: [ 'id', 'live_plan_id', 'product_id' ],
    });
    return results;
  }

  async getLivePlanDetailById(id: number) {
    const result = await this.app.mysql.get('live_plan_detail', { id });
    return result;
  }

  async livePlanAddProduct(livePlanId: number, productIds: number[]) {
    const maxSortOrderResult = await this.app.mysql.query(
      'SELECT MAX(sort_order) as maxSortOrder FROM live_plan_detail WHERE live_plan_id = ?',
      [ livePlanId ],
    );

    const maxSortOrder = maxSortOrderResult[0].maxSortOrder || 0;

    // 检查商品是否已经存在于计划中
    const existingProducts = await this.app.mysql.select('live_plan_detail', {
      where: { live_plan_id: livePlanId, product_id: productIds },
    });

    const existingProductIds = existingProducts.map(product => product.product_id);

    // 过滤掉已经存在的商品
    const productsToAdd = productIds.filter(productId => !existingProductIds.includes(productId));

    // 如果没有需要添加的商品，直接返回
    if (productsToAdd.length === 0) {
      return { affectedRows: 0 };
    }

    // 设置插入数据的sort_order
    const insertData = productsToAdd.map((productId, index) => ({
      live_plan_id: livePlanId,
      product_id: productId,
      sort_order: maxSortOrder + index + 1, // 设置sort_order为递增顺序
    }));

    // 插入数据
    const result = await this.app.mysql.insert('live_plan_detail', insertData);
    return result;
  }

  async livePlanUpdateSortProduct(sortItems: { id: number, sort_order: number }[]) {
    // 开启事务
    const conn = await this.app.mysql.beginTransaction();

    try {
      for (const item of sortItems) {
        await conn.update('live_plan_detail', { sort_order: item.sort_order }, { where: { id: item.id } });
      }

      // 提交事务
      await conn.commit();
    } catch (err) {
      // 回滚事务
      await conn.rollback();
      throw err;
    }
  }

  async livePlanUpdateProductInfo(id: number, data: { note: string } | { confirmed: boolean } | { is_live: boolean }) {
    return await this.app.mysql.update('live_plan_detail', { ...data }, { where: { id } });
  }

  async livePlanDeleteProduct(live_plan_id: number, ids: number[]) {
    const res = await this.app.mysql.get('live_plan', { live_plan_id });
    if (res?.is_locked) {
      return false;
    }
    return await this.app.mysql.delete('live_plan_detail', { id: ids });
  }

  async getLivePlan(livePlanId: number) {
    return await this.app.mysql.get('live_plan', { live_plan_id: livePlanId });
  }

  async getLivePlanDetails(livePlanId: number) {
    const sql = `
    SELECT
      lpd.*,
      p.product_name,
      p.main_images,
      p.code,
      p.category,
      p.style,
      p.inlay,
      p.open,
      p.state,
      p.location,
      p.create_date,
      p.create_people_id,
      p.supplier_id,
      p.spec_lists,
      p.update_date,
      JSON_ARRAYAGG(
          JSON_OBJECT(
            'sku_id', product_sku.sku_id,
            'sku_code', product_sku.sku_code,
            'cost_price', product_sku.cost_price,
            'sales_price', product_sku.sales_price,
            'tag_price', product_sku.tag_price,
            'live_price', product_sku.live_price,
            'distribution_price', product_sku.distribution_price,
            'stock_quantity', product_sku.stock_quantity,
            'weight', product_sku.weight,
            'size', product_sku.size,
            'image', product_sku.image,
            'state', product_sku.state,
            'specs', product_sku.specs,
            'created_at', product_sku.created_at,
            'updated_at', product_sku.updated_at
            )
        ) as skus
      FROM
        live_plan_detail lpd
      LEFT JOIN
        product p ON lpd.product_id = p.product_id
      LEFT JOIN
        product_sku ON lpd.product_id = product_sku.product_id
      WHERE
        lpd.live_plan_id = ${livePlanId}
      GROUP BY
        lpd.id, p.product_id, p.product_name, p.main_images, p.code, p.category, p.style,
        p.inlay, p.open, p.state, p.location, p.create_date, p.create_people_id,
        p.supplier_id, p.spec_lists, p.update_date;
    `;

    const results = await this.app.mysql.query(sql);
    return results;
  }

  async createLivePlanDetailLogs(param: {
    user_id: number,
    live_plan_id: number,
    live_plan_detail_id: number,
    /**
     * @name operation_type
     * @param 'add' 添加
     * @param 'update' 更新
     * @param 'delete' 删除
     */
    operation_type: 'add' | 'update' | 'delete',
    operation_description: string,
  }) {
    const result = await this.app.mysql.insert('live_plan_detail_log', param);
    return result;
  }

  /**
   * 检查商品是否已经存在于货盘中
   * @param livePlanId 货盘ID
   * @param productIds 要检查的商品ID数组
   * @return {Promise<Array<{id: number, live_plan_id: number, product_id: number}>>} 已存在于货盘中的商品列表
   */
  async getExistingProductsInLivePlan(livePlanId: number, productIds: number[]): Promise<Array<{id: number, live_plan_id: number, product_id: number}>> {
    // 查询已经存在于货盘中的商品
    const existingProducts = await this.app.mysql.select('live_plan_detail', {
      where: { live_plan_id: livePlanId, product_id: productIds },
      columns: [ 'id', 'live_plan_id', 'product_id' ],
    });

    return existingProducts;
  }
}
