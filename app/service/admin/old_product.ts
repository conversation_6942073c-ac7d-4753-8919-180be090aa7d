import { Service } from 'egg';
import dayjs from 'dayjs';
export default class Product extends Service {

  async createProduct(param: any) {
    const result = await this.app.mysql.insert('old_product', param);
    return result;
  }

  async removeProduct(product_id: number) {
    const result = await this.app.mysql.delete('old_product', { product_id });
    return result;
  }

  async getProductList({ sql, countSql }: { sql: string, countSql: string }) {
    const { app } = this;
    const data = await app.mysql.query(sql);
    const count = await app.mysql.query(countSql);
    const total = count[0].total;
    return {
      data,
      total,
    };
  }

  async getProductListTotal(sql: string) {
    const { app } = this;
    const countResult = await app.mysql.query(sql);
    const total = countResult[0].total;
    return total;
  }

  async updateProduct(param: any, product_id: number) {
    const result = await this.app.mysql.update('old_product', param, {
      where: {
        product_id,
      },
    });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }


  async insertSku(sql: string) {
    const result = await this.app.mysql.query(sql);
    return result;
  }

  async updateSku(param: any, sku_id: number) {
    const result = await this.app.mysql.update('sku', param, {
      where: {
        sku_id,
      },
    });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async deleteSku(sku_id: number) {
    const result = await this.app.mysql.delete('sku', {
      sku_id,
    });
    const deleteSuccess = result.affectedRows === 1;
    return deleteSuccess;
  }

  async updateProductInventory({ type, num, sku_id }: { type: 'add' | 'reduce', num: number, sku_id: number }) {
    const sql = `UPDATE sku
                   SET inventory = inventory ${type === 'add' ? '+' : '-'} ${num}
                   WHERE sku_id = ${sku_id}`;
    const result = await this.app.mysql.query(sql);
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async updateProductTotalInventory({ type, num, product_id }: { type: 'add' | 'reduce', num: number, product_id: number }) {
    const sql = `UPDATE product
                   SET total_inventory = total_inventory ${type === 'add' ? '+' : '-'} ${num}
                   WHERE product_id = ${product_id}`;
    const result = await this.app.mysql.query(sql);
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async deleteProduct(product_id: number) {
    const result = await this.app.mysql.update('old_product',
      {
        state: 5,
        update_date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        where: {
          product_id,
        },
      });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async getProductDetails(product_id: number) {
    const { app } = this;
    const product_details = await app.mysql.get('old_product', { product_id });
    return product_details;
  }

  async getProductSku(product_id: number) {
    const { app } = this;
    const product_sku = await app.mysql.select('sku', { where: { product_id } });
    return product_sku;
  }

  async query({ sql }: { sql: string }) {
    const { app } = this;
    const result = await app.mysql.query(sql);
    return result;
  }

  async specTotal() {
    const { app } = this;
    const total = await app.mysql.count('spec_name');
    return total;
  }

  async listSpecs(page, pageSize) {
    const offset = (page - 1) * pageSize;

    // 查询所有的spec_name总数
    const totalResult = await this.app.mysql.query('SELECT COUNT(*) AS total FROM spec_name');
    const total = totalResult[0].total;

    // 查询分页的spec_name
    const specNames = await this.app.mysql.select('spec_name', {
      limit: pageSize,
      offset,
    });

    // 查询所有的spec_value
    const specValues = await this.app.mysql.select('spec_value');

    // 组合结果
    const data = specNames.map(specName => {
      return {
        spec_id: specName.spec_id,
        spec_name: specName.spec_name,
        values: specValues
          .filter(value => value.spec_id === specName.spec_id)
          .map(value => ({
            spec_value_id: value.spec_value_id,
            spec_value_name: value.spec_value_name,
          })),
      };
    });

    return {
      total,
      current: page,
      pageSize,
      data,
    };
  }

  async updateSpecName(specId, specName) {
    const result = await this.app.mysql.update('spec_name', { spec_name: specName },
      {
        where: {
          spec_id: specId,
        },
      },
    );
    return result;
  }

  async updateSpecValue(specValueId, specValueName) {
    const result = await this.app.mysql.update('spec_value', {
      spec_value_name: specValueName,
    }, {
      where: {
        spec_value_id: specValueId,
      },
    });
    return result;
  }

  async createSpecName(specName: string) {
    const result = await this.app.mysql.insert('spec_name', { spec_name: specName });
    return result;
  }

  async deleteSpecValues(specId) {
    const result = await this.app.mysql.delete('spec_value', { spec_id: specId });
    return result;
  }

  async deleteSpecName(specId) {
    const result = await this.app.mysql.delete('spec_name', { spec_id: specId });
    return result;
  }

  async createSpecValue(specId: number, specValueName: string) {
    const result = await this.app.mysql.insert('spec_value', { spec_id: specId, spec_value_name: specValueName });
    return result;
  }

}
