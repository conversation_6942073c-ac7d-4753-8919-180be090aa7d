import { Service } from 'egg';

export default class Logs extends Service {

  async createUpdateProductLogs(param: {
    user_id: number,
    product_id: number,
    /**
         * @name operation_type
         * @param 0 update
         * @param 1 delete
         */
    operation_type: 0 | 1,
    operation_description: string,
  }) {
    const result = await this.app.mysql.insert('product_logs', param);
    return result;
  }


  async createOrderLogs(param: {
    user_id: number,
    order_id: number,
    operation_type: string,
    operation_description: string,
  }) {
    const result = await this.app.mysql.insert('order_logs', param);
    return result;
  }

  async createRequestLogs(param: any) {
    const result = await this.app.mysql.insert('request_logs', param);
    return result;
  }

  /**
   * 创建登录日志
   */
  async createLoginLogs(param: {
    user_id: number,
    username: string,
    login_type: 'admin' | 'customer',
    ip: string,
    user_agent: string,
    login_status: 'success' | 'fail',
    fail_reason?: string,
  }) {
    const result = await this.app.mysql.insert('login_logs', {
      ...param,
      login_time: new Date(),
      created_at: new Date(),
    });
    return result;
  }

  /**
   * 获取登录日志列表
   */
  async getLoginLogs({ sql, countSql }: { sql: string, countSql: string }) {
    const { app } = this;
    const data = await app.mysql.query(sql);
    const count = await app.mysql.query(countSql);
    const total = count[0].total;
    return {
      data,
      total,
    };
  }

  async getRequestLogs({ sql, countSql }: { sql: string, countSql: string }) {
    const { app } = this;
    const data = await app.mysql.query(sql);
    const count = await app.mysql.query(countSql);
    const total = count[0].total;
    return {
      data,
      total,
    };
  }
}
