import { Service } from 'egg';
import dayjs from 'dayjs';
export default class User extends Service {

  async updateAdminUserState(user_id: number, state: number) {
    const result = await this.app.mysql.update('admin_user',
      {
        state,
      }, {
        where: {
          user_id,
        },
      });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async updateAdminUserPassword(user_id: number, password: string) {
    const result = await this.app.mysql.update('admin_user',
      {
        password,
      }, {
        where: {
          user_id,
        },
      });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async createAdminUser(param: any) {
    const result = await this.app.mysql.insert('admin_user', param);
    const insertSuccess = result.affectedRows === 1;
    return insertSuccess;
  }

  async updateAdminUser(param: any, user_id: number) {
    const result = await this.app.mysql.update('admin_user', param, {
      where: {
        user_id,
      },
    });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async getUserProfilie(user_id: number) {
    const result = await this.app.mysql.get('admin_user', { user_id }, {
      columns: [ 'user_id', 'avatar', 'real_name', 'username', 'phone', 'email', 'user_role', 'create_date', 'last_date', 'state' ],
    });
    return result;
  }

  async getUserInfo(data: { username: string } | { user_id: number } | { phone: number }, columns: string[] = []) {
    const { app } = this;
    const userinfo = await app.mysql.get('admin_user', { ...data }, columns ? { columns } : {});
    return userinfo;
  }

  async updateUserInfoLastDate(user_id: number) {
    const { app } = this;
    const result = await app.mysql.update('admin_user',
      { last_date: dayjs().format('YYYY-MM-DD HH:mm:ss') },
      {
        where: {
          user_id,
        },
      });
    return result;
  }

  async getUserInfoList({ current, pageSize, condition }) {
    const { app } = this;
    const offset = (current - 1) * pageSize;
    const countSql = `SELECT COUNT(*) as total FROM admin_user ${condition}`;
    const countResult = await app.mysql.query(countSql);
    const total = countResult[0].total;

    const sql = `SELECT user_id,avatar,username,real_name,phone,email,create_date,last_date,state,user_role,create_people FROM admin_user ${condition} LIMIT ${pageSize} OFFSET ${offset}`;
    const result = await app.mysql.query(sql);

    return {
      total,
      data: result,
    };
  }

  // Customer
  async createCustomerUser(param: any) {
    const result = await this.app.mysql.insert('customer_user', param);
    const insertSuccess = result.affectedRows === 1;
    return insertSuccess;
  }

  async updateCustomerUser(param: any, customer_id: number) {
    const result = await this.app.mysql.update('customer_user', param, {
      where: {
        customer_id,
      },
    });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async getCustomerUserList({ current, pageSize, condition }) {
    const { app } = this;
    const offset = (current - 1) * pageSize;
    const countSql = `SELECT COUNT(*) as total FROM customer_user ${condition}`;
    const countResult = await app.mysql.query(countSql);
    const total = countResult[0].total;

    const sql = `SELECT city,id_card_id,id_card_images,customer_id,avatar,username,customer_name,phone,email,create_date,last_date,state,create_people,address,note,type FROM customer_user ${condition} LIMIT ${pageSize} OFFSET ${offset}`;
    const result = await app.mysql.query(sql);

    return {
      total,
      data: result,
    };
  }

  async updateCustomerUserState(customer_id: number, state: number) {
    const result = await this.app.mysql.update('customer_user',
      {
        state,
      }, {
        where: {
          customer_id,
        },
      });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async getCustomerUserInfo(data: { username: string } | { customer_id: number } | { phone: number }, columns: string[] = []) {
    const { app } = this;
    const userinfo = await app.mysql.get('customer_user', { ...data }, columns ? { columns } : {});
    return userinfo;
  }

  async updateCustomerUserPassword(customer_id: number, password: string) {
    const result = await this.app.mysql.update('customer_user',
      {
        password,
      }, {
        where: {
          customer_id,
        },
      });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }


  // Supplier
  async createSupplierUser(param: any) {
    const result = await this.app.mysql.insert('supplier_user', param);
    const insertSuccess = result.affectedRows === 1;
    return insertSuccess;
  }

  async getSupplierUserList({ current, pageSize, condition }) {
    const { app } = this;
    const offset = (current - 1) * pageSize;
    const countSql = `SELECT COUNT(*) as total FROM supplier_user ${condition}`;
    const countResult = await app.mysql.query(countSql);
    const total = countResult[0].total;

    const sql = `SELECT supplier_id,avatar,username,supplier_name,contacts,credentials_image,phone,email,create_date,last_date,state,create_people,address,note FROM supplier_user ${condition} LIMIT ${pageSize} OFFSET ${offset}`;
    const result = await app.mysql.query(sql);

    return {
      total,
      data: result,
    };
  }

  async getSupplierUserInfo(data: { username: string } | { supplier_id: number } | { phone: number }, columns: string[] = []) {
    const { app } = this;
    const userinfo = await app.mysql.get('supplier_user', { ...data }, columns ? { columns } : {});
    return userinfo;
  }

  async updateSupplierUserPassword(supplier_id: number, password: string) {
    const result = await this.app.mysql.update('supplier_user',
      {
        password,
      }, {
        where: {
          supplier_id,
        },
      });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async updateSupplierUser(param: any, supplier_id: number) {
    const result = await this.app.mysql.update('supplier_user', param, {
      where: {
        supplier_id,
      },
    });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async updateSupplierUserState(supplier_id: number, state: number) {
    const result = await this.app.mysql.update('supplier_user',
      {
        state,
      }, {
        where: {
          supplier_id,
        },
      });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

}
