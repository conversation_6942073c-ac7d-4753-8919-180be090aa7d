import { Service } from 'egg';

export default class Data extends Service {

  async getSupplierUserDataEnum({ sql }: { sql: string }) {
    const { app } = this;
    const result = await app.mysql.query(sql);
    return result;
  }

  async getCustomerUserDataEnum({ sql }: { sql: string }) {
    const { app } = this;
    const result = await app.mysql.query(sql);
    return result;
  }

  async getAdminUserDataEnum({ sql }: { sql: string }) {
    const { app } = this;
    const result = await app.mysql.query(sql);
    return result;
  }

  async getTotalAnalysis() {
    const { app } = this;
    const todayOrderSql = 'SELECT COUNT(*) as todayOrder FROM order_data WHERE DATE(created_at) = CURDATE();';
    const todayOrder = await app.mysql.query(todayOrderSql);
    const totalOrder = await app.mysql.count('order_data');

    const todayGmvSql = `
                        SELECT SUM(oi.quantity * oi.unit_price) AS todayGmv
                        FROM order_item oi
                        JOIN order_data od ON oi.order_id = od.order_id
                        WHERE DATE(od.created_at) = CURDATE();
                        `;
    const totalGmvSql = `
                        SELECT SUM(oi.quantity * oi.unit_price) AS totalGmv
                        FROM order_item oi
                        JOIN order_data od ON oi.order_id = od.order_id;
                        `;
    const todayGmv = await app.mysql.query(todayGmvSql);
    const totalGmv = await app.mysql.query(totalGmvSql);

    const todayCustomerSql = 'SELECT COUNT(*) as todayCustomer FROM customer_user WHERE DATE(create_date) = CURDATE();';
    const todayCustomer = await app.mysql.query(todayCustomerSql);
    const totalCustomer = await app.mysql.count('customer_user');

    const totalProduct = await app.mysql.count('product');
    const totalSupplier = await app.mysql.count('supplier_user');

    return {
      totalOrder,
      todayOrder: todayOrder[0]?.todayOrder,
      totalGmv: totalGmv[0]?.totalGmv,
      todayGmv: todayGmv[0]?.todayGmv || 0,
      totalProduct,
      totalCustomer,
      totalSupplier,
      todayCustomer: todayCustomer[0]?.todayCustomer || 0,
    };
  }

  async getOrderAnalysis() {
    const { app } = this;
    const sql = `
    SELECT  
        SUM(CASE WHEN order_status = 'pending' THEN 1 ELSE 0 END) AS pendingOrder,
        SUM(CASE WHEN order_status = 'processing' THEN 1 ELSE 0 END) AS processingOrder,
        SUM(CASE WHEN order_status = 'shipped' THEN 1 ELSE 0 END) AS shippedOrder,
        SUM(CASE WHEN order_status = 'delivered' THEN 1 ELSE 0 END) AS deliveredOrder,
        SUM(CASE WHEN order_status = 'cancelled' THEN 1 ELSE 0 END) AS cancelledOrder,
        SUM(CASE WHEN order_status = 'returned' THEN 1 ELSE 0 END) AS returnedOrder,
        SUM(CASE WHEN order_status = 'returning' THEN 1 ELSE 0 END) AS returningOrder,
        SUM(CASE WHEN order_status = 'after_sales' THEN 1 ELSE 0 END) AS afterSalesOrder,
        COUNT(*) as totalOrder
    FROM order_data;
    `;
    const result = await app.mysql.query(sql);
    return { ...result?.[0] };
  }

  async getPendingOrder() {
    const { app } = this;
    const totalPendingOrder = await app.mysql.count('order_data', { order_status: 'pending' });
    return totalPendingOrder;
  }

  async getGmv({ sql }: { sql: string }) {
    const { app } = this;
    const result = await app.mysql.query(sql);
    return result;
  }

  async select({ sql }: { sql: string }) {
    const { app } = this;
    const result = await app.mysql.query(sql);
    return result;
  }
}
