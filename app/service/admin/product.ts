import { Service } from 'egg';
export default class Product extends Service {

  async createProduct(param: any) {
    const result = await this.app.mysql.insert('product', param);
    return result;
  }

  async createProductDetails(product_id, details) {
    const result = await this.app.mysql.insert('product_details', { product_id, ...details });
    return result;
  }

  async createProductSku(product_id, sku) {
    const result = await this.app.mysql.insert('product_sku', { product_id, ...sku });
    return result;
  }

  async isCodeExists(code) {
    const result = await this.app.mysql.select('product', {
      where: { code },
      columns: [ 'product_id' ],
    });
    return result.length > 0;
  }

  async isProductExists(product_id: number) {
    const result = await this.app.mysql.select('product', {
      where: { product_id },
      columns: [ 'product_id' ],
    });
    return result.length > 0;
  }

  async isSkuCodeExists(sku_code: string) {
    const result = await this.app.mysql.select('product_sku', {
      where: { sku_code },
      columns: [ 'sku_id' ],
    });
    return result.length > 0;
  }


  async getProductList({ sql, countSql }: { sql: string, countSql: string }) {
    const { app } = this;
    const data = await app.mysql.query(sql);
    const count = await app.mysql.query(countSql);
    const total = count[0].total;
    return {
      data,
      total,
    };
  }

  async updateProduct(product_id: number, data) {
    const result = await this.app.mysql.update('product', { ...data }, { where: { product_id } });
    return result;
  }

  async updateProductDetails(product_id: number, data) {
    const result = await this.app.mysql.update('product_details', { ...data }, { where: { product_id } });
    return result;
  }

  async updateProductSku(sku_id: number, data) {
    return await this.app.mysql.update('product_sku', { ...data }, { where: { sku_id } });
  }

  async deleteProduct(product_id: number) {
    const result = await this.app.mysql.update('product',
      {
        state: 5,
      },
      {
        where: {
          product_id,
        },
      });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async getProductById(product_id: number) {
    return await this.app.mysql.get('product', { product_id });
  }

  async getProductDetailsById(product_id: number) {
    return await this.app.mysql.get('product_details', { product_id });
  }

  async getProductSkusById(product_id: number) {
    return await this.app.mysql.select('product_sku', {
      where: { product_id },
    });
  }


  async removeProduct(product_id: number) {
    const result = await this.app.mysql.delete('product', { product_id });
    return result;
  }

  async getProductListTotal(sql: string) {
    const { app } = this;
    const countResult = await app.mysql.query(sql);
    const total = countResult[0].total;
    return total;
  }

  async insertSku(sql: string) {
    const result = await this.app.mysql.query(sql);
    return result;
  }

  async updateSku(param: any, sku_id: number) {
    const result = await this.app.mysql.update('sku', param, {
      where: {
        sku_id,
      },
    });
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async deleteSku(sku_id: number) {
    const result = await this.app.mysql.delete('sku', {
      sku_id,
    });
    const deleteSuccess = result.affectedRows === 1;
    return deleteSuccess;
  }

  async updateProductInventory({ type, num, sku_id }: { type: 'add' | 'reduce', num: number, sku_id: number }) {
    const sql = `UPDATE product_sku
                   SET stock_quantity = stock_quantity ${type === 'add' ? '+' : '-'} ${num}
                   WHERE sku_id = ${sku_id}`;
    const result = await this.app.mysql.query(sql);
    const updateSuccess = result.affectedRows === 1;
    return updateSuccess;
  }

  async getProductDetails(product_id: number) {
    const { app } = this;
    const product_details = await app.mysql.get('product', { product_id });
    return product_details;
  }

  async getProductSkus(product_id: number) {
    const { app } = this;
    const product_sku = await app.mysql.select('product_sku', { where: { product_id } });
    return product_sku;
  }

  async deleteProductSku(sku_id: number) {
    const { app } = this;
    const deleteSku = await app.mysql.delete('product_sku', { sku_id });
    return deleteSku;
  }

  async query({ sql }: { sql: string }) {
    const { app } = this;
    const result = await app.mysql.query(sql);
    return result;
  }

  async specTotal() {
    const { app } = this;
    const total = await app.mysql.count('spec_name');
    return total;
  }

  async listSpecs(page, pageSize) {
    const offset = (page - 1) * pageSize;

    // 查询所有的spec_name总数
    const totalResult = await this.app.mysql.query('SELECT COUNT(*) AS total FROM spec_name');
    const total = totalResult[0].total;

    // 查询分页的spec_name
    const specNames = await this.app.mysql.select('spec_name', {
      limit: pageSize,
      offset,
    });

    // 查询所有的spec_value
    const specValues = await this.app.mysql.select('spec_value');

    // 组合结果
    const data = specNames.map(specName => {
      return {
        spec_id: specName.spec_id,
        spec_name: specName.spec_name,
        values: specValues
          .filter(value => value.spec_id === specName.spec_id)
          .map(value => ({
            spec_value_id: value.spec_value_id,
            spec_value_name: value.spec_value_name,
          })),
      };
    });

    return {
      total,
      current: page,
      pageSize,
      data,
    };
  }

  async updateSpecName(specId, specName) {
    const result = await this.app.mysql.update('spec_name', { spec_name: specName },
      {
        where: {
          spec_id: specId,
        },
      },
    );
    return result;
  }

  async updateSpecValue(specValueId, specValueName) {
    const result = await this.app.mysql.update('spec_value', {
      spec_value_name: specValueName,
    }, {
      where: {
        spec_value_id: specValueId,
      },
    });
    return result;
  }

  async createSpecName(specName: string) {
    const result = await this.app.mysql.insert('spec_name', { spec_name: specName });
    return result;
  }

  async deleteSpecValues(specId) {
    const result = await this.app.mysql.delete('spec_value', { spec_id: specId });
    return result;
  }

  async deleteSpecName(specId) {
    const result = await this.app.mysql.delete('spec_name', { spec_id: specId });
    return result;
  }

  async createSpecValue(specId: number, specValueName: string) {
    const result = await this.app.mysql.insert('spec_value', { spec_id: specId, spec_value_name: specValueName });
    return result;
  }

  // 记录商品SKU价格历史
  async recordProductPriceHistory(params: {
    sku_id: number,
    product_id: number,
    tag_price: number,
    cost_price?: number,
    sales_price?: number,
    distribution_price?: number,
    live_price?: number,
    operator_id: number,
    operation_type: 'create' | 'update', // create: 新增, update: 更新
  }) {
    const { app } = this;
    const result = await app.mysql.insert('product_price_history', {
      ...params,
      created_at: new Date(),
    });
    return result.affectedRows === 1;
  }

  // 查询商品价格历史记录 - 仅使用product_id查询，不需要时间筛选
  async getProductPriceHistory(product_id: number) {
    const { app } = this;

    // 使用原生SQL查询，连接product_price_history和product_sku表，获取specs信息
    const sql = `
      SELECT pph.*, ps.specs
      FROM product_price_history pph
      LEFT JOIN product_sku ps ON pph.sku_id = ps.sku_id
      WHERE pph.product_id = ?
      ORDER BY pph.created_at DESC
    `;

    return await app.mysql.query(sql, [ product_id ]);
  }

}
