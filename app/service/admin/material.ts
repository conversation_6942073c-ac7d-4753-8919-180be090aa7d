import { Service } from 'egg';

export default class MaterialService extends Service {
  // 获取素材列表（分页 + 筛选）
  async listMaterials(params: {
    product_id: number;
    page: number;
    pageSize: number;
    type?: 'image' | 'video' | 'document' | string;
    tag?: string;
  }) {
    const { page = 1, pageSize = 10, type, tag, product_id } = params;
    const offset = (page - 1) * pageSize;

    let where = 'WHERE product_id = ?';
    const values: any[] = [ product_id ];

    if (type) {
      where += ' AND type = ?';
      values.push(type);
    }

    if (tag) {
      where += ' AND JSON_CONTAINS(tags, ?)';
      values.push(JSON.stringify([ tag ]));
    }

    const query = `
      SELECT * FROM material
      ${where}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const countQuery = `
      SELECT COUNT(*) AS total FROM material
      ${where}
    `;

    const materials = await this.app.mysql.query(query, [ ...values, pageSize, offset ]);
    const totalResult = await this.app.mysql.query(countQuery, values);

    return {
      data: materials.map((m: any) => ({
        ...m,
        tags: m.tags ? JSON.parse(m.tags) : [],
      })),
      total: totalResult[0].total,
    };
  }

  // 上传素材
  async createMaterial(data: {
    product_id: number;
    name: string;
    type: 'image' | 'video' | 'document';
    url: string;
    tags?: string[];
  }) {
    // 检查素材数量限制
    const count = await this.app.mysql.count('material', { product_id: data.product_id });
    if (count >= 100) {
      throw new Error('每个商品最多上传100个素材');
    }

    return this.app.mysql.insert('material', {
      ...data,
      tags: data.tags ? JSON.stringify(data.tags) : null,
    });
  }

  // 更新素材
  async updateMaterial(id: number, data: { name?: string; tags?: string[], sort_order: number }) {
    const updateFields: any = {};
    if (data.name) updateFields.name = data.name;
    if (data.tags) updateFields.tags = JSON.stringify(data.tags);
    if (data.sort_order) updateFields.sort_order = data.sort_order;

    return this.app.mysql.update('material', updateFields, {
      where: { id },
    });
  }

  // 删除素材
  async deleteMaterial(id: number) {
    return this.app.mysql.delete('material', { id });
  }
}
