import { Service } from 'egg';

export default class CustomerBox extends Service {
  async insCustomerBox(params:{
    customer_id: string
    box_name: string
    box_quantity: number
    image: string
    warehouse: string
  }) {
    const { app } = this;
    const result = await app.mysql.insert('customer_box', params);
    if (result.affectedRows === 1) {
      return result.insertId; // 返回插入的 box_id
    }
    return false;
  }

  async delCustomerBox(box_id: string | number) {
    const { app } = this;
    const result = await app.mysql.delete('customer_box', { box_id });
    return result.affectedRows === 1;
  }

  async getBoxList(current: number, pageSize: number, params:{
    customer_id: number | string;
  }) {
    const { app } = this;
    const offset = (current - 1) * pageSize;
    const boxList = await app.mysql.select('customer_box', {
      where: { ...params },
      limit: pageSize,
      offset,
    });
    return {
      total: await app.mysql.count('customer_box', { ...params }),
      data: boxList,
    };
  }

  async getBoxDetails(box_id: number | string) {
    const { app } = this;
    const boxDetails = await app.mysql.get('customer_box', {
      box_id,
    });
    return boxDetails;
  }

  async getBoxLog(box_id:number | string, current: number, pageSize: number) {
    const { app } = this;
    const offset = (current - 1) * pageSize;
    const boxList = await app.mysql.select('customer_box_log', {
      where: { box_id },
      limit: pageSize,
      offset,
    });
    return {
      total: await app.mysql.count('customer_box_log', { box_id }),
      data: boxList,
    };
  }

  async updateCustomerBoxQuantity(box_id: string | number, quantity: number, type: 'increase' | 'decrease') {
    const { app } = this;

    // 如果是减少操作，先检查库存是否充足
    if (type === 'decrease') {
      const box = await app.mysql.get('customer_box', { box_id });
      if (!box || box.box_quantity < quantity) {
        // 如果库存不足，返回特殊标记
        return { success: false, message: '库存不足' };
      }
    }

    // 使用 MySQL 自带的加减方法
    const query = type === 'increase'
      ? 'UPDATE customer_box SET box_quantity = box_quantity + ? WHERE box_id = ?'
      : 'UPDATE customer_box SET box_quantity = box_quantity - ? WHERE box_id = ?';

    const result = await app.mysql.query(query, [ quantity, box_id ]);

    return { success: result.affectedRows === 1 };
  }

  async updateCustomerBoxInfo(box_id: string | number, updateData: Record<string, any>) {
    const { app } = this;
    const result = await app.mysql.update('customer_box', {
      ...updateData,
      updated_at: new Date(),
    }, {
      where: { box_id },
    });

    return result.affectedRows === 1;
  }

  async insBoxLog(params: {
    box_id: string | number;
    operation_type: string; // 操作类型: 'add', 'reduce', 'delete', 'update'
    quantity: number;
    operator: string;
    note?: string;
    order_id?: string | number; // 关联订单ID
  }) {
    const { app } = this;
    const now = new Date();
    const result = await app.mysql.insert('customer_box_log', {
      ...params,
      created_at: now,
    });
    return result.affectedRows === 1;
  }
}
