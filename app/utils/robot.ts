import { Context } from 'egg';

export const QY_PRODUCT_ROBOT = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=cb9c38f0-4dd6-43ae-b017-ba822f96d8f9';

export const SendQyMessage = async ({
  ctx,
  data,
}: {
  ctx: Context,
  data: string | unknown
}) => {
  const isDev = process.env.NODE_ENV === 'development';
  if (isDev) {
    return false;
  }
  return ctx.curl(QY_PRODUCT_ROBOT, {
    // 必须指定 method
    method: 'POST',
    // 通过 contentType 告诉 HttpClient 以 JSON 格式发送
    contentType: 'json',
    data,
    // 明确告诉 HttpClient 以 JSON 格式处理返回的响应 body
    dataType: 'json',
  });

};
