import argon2 from 'argon2';
import CryptoJS from 'crypto-js';

// 密码转hash
export const hashPassword = async (text: string) => {
  const options = {
    type: argon2.argon2id,
    timeCost: 2,
    memoryCost: 10 * 1024, // 10 MB
    parallelism: 2,
    hashLength: 32,
  };
  const hashValue = await argon2.hash(text, options);
  return hashValue;
};

export const verifyPassword = async (hash: string, text: string) => {
  return await argon2.verify(hash, text);
};

export const decryptedText = (password: string) => {
  const bytes = CryptoJS.AES.decrypt(password, '<EMAIL>');
  const text = bytes.toString(CryptoJS.enc.Utf8);
  return text;
};

export const generateRandomNumericCode = (length: number) => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += Math.floor(Math.random() * 10);
  }
  return result;
};

// 生成随机密码
export const generateRandomPassword = () => {
  const length = 12;
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let retVal = '';
  for (let i = 0, n = charset.length; i < length; ++i) {
    retVal += charset.charAt(Math.floor(Math.random() * n));
  }
  return retVal;
};
