import { Context } from 'egg';
import { requestLogs } from './logs';

export const successRes = ({
  ctx,
  data = null,
  message = '',
  isLog = false,
}: {
  ctx: Context,
  data?: PaginationApiResponse | null | unknown
  message?: string,
  isLog?: boolean
}) => {
  ctx.status = 200;
  ctx.body = {
    code: 200,
    success: true,
    message,
    data,
  };
  if (isLog) {
    // requestLogs(ctx);
  }
};

export const failRes = ({
  ctx,
  data = null,
  message = '',
  code = 400,
  isLog = true,
}: {
  ctx: Context,
  data?: PaginationApiResponse | null | unknown,
  message?: string,
  code?: number,
  isLog?: boolean
}) => {
  ctx.status = code;
  ctx.body = {
    code,
    success: false,
    message,
    data,
  };
  if (isLog) {
    requestLogs(ctx);
  }
};
