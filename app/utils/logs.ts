import { Context } from 'egg';

export const getDataChange = (old: Record<any, any>, now: Record<any, any>) => {
  const changes = {};
  for (const key in now) {
    if (old[key] !== now[key] && !key.includes('date') && !key.includes('at')) {
      changes[key] = {
        old: old[key],
        new: now[key],
      };
    }
  }
  return changes;
};

export const getDataChangeString = (data: Record<any, { old: string, new: string | number }>) => {
  let str = '';
  for (const key in data) {
    str += key + `:${data[key].old}=>${data[key].new};`;
  }
  return str;
};

export const requestLogs = async (ctx: Context) => {
  if (ctx.method === 'GET' || ctx.method === 'get') {
    return false;
  }
  const logObjs = {
    request_id: ctx?.requestId || '',
    method: ctx?.method || '',
    url: ctx?.url || '',
    body: ctx.request?.body ? JSON.stringify(ctx.request?.body) : '',
    user_id: ctx.state?.user_id || '',
    code: ctx?.response?.status || '',
    ip: ctx?.ip || '',
  };
  ctx.service.admin.logs.createRequestLogs(logObjs);
};


// 对比两个对象的差异
export const compareObjects = (original, updated) => {
  const changes = {};
  for (const key in updated) {
    if (original[key] !== updated[key]) {
      changes[key] = {
        old: original[key],
        new: updated[key],
      };
    }
  }
  return changes;
};
// 对比两个 SKU 列表的差异
export const compareSkus = (originalSkus, updatedSkus) => {
  const changes = {};
  const originalSkuMap = originalSkus.reduce((map, sku) => {
    map[sku?.sku_id] = sku;
    return map;
  }, {});

  const updatedSkuMap = updatedSkus.reduce((map, sku) => {
    map[sku.sku_id] = sku;
    return map;
  }, {});

  for (const skuId in updatedSkuMap) {
    if (originalSkuMap[skuId]) {
      const skuChanges = compareObjects(originalSkuMap[skuId], updatedSkuMap[skuId]);
      if (Object.keys(skuChanges).length > 0) {
        changes[skuId] = skuChanges;
      }
    } else {
      changes[skuId] = { added: updatedSkuMap[skuId] };
    }
  }

  for (const skuId in originalSkuMap) {
    if (!updatedSkuMap[skuId]) {
      changes[skuId] = { removed: originalSkuMap[skuId] };
    }
  }

  return changes;
};

// 格式化差异信息
export const formatChanges = (productChanges, productDetailsChanges, skuChanges) => {
  let changesStr = '';

  if (Object.keys(productChanges).length > 0) {
    changesStr += '商品信息变更: ';
    for (const key in productChanges) {
      if ([ 'create_date', 'update_date', 'created_at', 'updated_at' ].includes(key)) {
        break;
      }
      changesStr += `${key}: ${productChanges[key].old} => ${productChanges[key].new}, `;
    }
  }

  if (Object.keys(productDetailsChanges).length > 0) {
    changesStr += '商品详情变更: ';
    for (const key in productDetailsChanges) {
      changesStr += `${key}: ${productDetailsChanges[key].old} => ${productDetailsChanges[key].new}, `;
    }
  }

  if (Object.keys(skuChanges).length > 0) {
    changesStr += 'SKU变更: ';
    for (const skuId in skuChanges) {
      if (skuChanges[skuId]?.added) {
        changesStr += `新增SKU: ${JSON.stringify(skuChanges[skuId]?.added)}, `;
      } else if (skuChanges[skuId]?.removed) {
        changesStr += `删除SKU: ${JSON.stringify(skuChanges[skuId]?.removed)}, `;
      } else {
        let hasOtherChanges = false;
        for (const key in skuChanges[skuId]) {
          if (key !== 'created_at' && key !== 'updated_at') {
            hasOtherChanges = true;
            break;
          }
        }
        if (hasOtherChanges) {
          changesStr += `SKU变更: ${skuId}, `;
          for (const key in skuChanges[skuId]) {
            if (key !== 'created_at' && key !== 'updated_at') {
              changesStr += `${key}: ${skuChanges[skuId][key].old} => ${skuChanges[skuId][key].new}, `;
            }
          }
        }
      }
    }
  }

  return changesStr.trim().replace(/,$/, '');
};
