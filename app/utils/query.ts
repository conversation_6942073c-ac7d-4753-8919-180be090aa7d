import { Dayjs } from 'dayjs';

export const queryFormat = (conditions: Record<string, any>) => {
  let conditionClause = '';
  if (conditions && Object.keys(conditions).length > 0) {
    const conditionArr: string[] = [];
    for (const key in conditions) {
      const value = conditions[key];
      if (value) {
        conditionArr.push(`${key} LIKE '%${value}%'`);
      }
    }
    conditionClause = conditionArr.join(' AND ');
  }
  return conditionClause ? conditionClause : '';
};

export type queryFormatType = {
  type: 'like' | 'accurate' | 'json' | 'date' | 'date_no_time',
  name: string,
  value: string
} | {
  type: 'range_data' | 'range_number',
  name: string,
  value: string[]
} | {
  type: 'state',
  name: string,
  valueType: 'True' | 'False'
  value: string
};

export type sortFormatType = {
  type: 'date' | 'other',
  name: string,
  value: 'ASC' | 'DESC'
};

export const sqlQueryFormat = ({ params = [], sort, table_name, pageSize, current, columns = [] }: {
  params?: queryFormatType[],
  sort?: sortFormatType | void,
  table_name: string,
  pageSize?: number,
  current?: number,
  columns?: string[] | void,
}) => {
  let sql = `SELECT ${columns && columns?.length > 0 ? columns?.toString() : '*'} FROM ${table_name}`;

  let whereClause = ' WHERE 1 = 1';

  params?.map(item => {
    if (item.type === 'accurate' && item.value) {
      whereClause += ` AND ${item.name} = ${item.value}`;
    }
    if (item.type === 'like' && item.value) {
      whereClause += ` AND ${item.name} LIKE '%${item.value}%'`;
    }
    if (item.type === 'json' && item.value) {
      whereClause += ` AND JSON_CONTAINS(${item.name}, '${item.value}')`;
    }
    if (item.type === 'state') {
      if (item.valueType === 'True') {
        whereClause += ` AND  ${item.name} = '${item.value}'`;
      } else {
        whereClause += ` AND  ${item.name} != '5'`;
      }
    }
    if (item.type === 'range_data' && item.value) {
      whereClause += ` AND ${item.name} BETWEEN '${item.value[0]}' AND '${item.value[1]}'`;
    }
    if (item.type === 'range_number' && item.value) {
      whereClause += ` AND ${item.name} BETWEEN '${item.value[0]}' AND '${item.value[1]}'`;
    }
    if (item.type === 'date' && item.value) {
      whereClause += ` AND ${item.name} = '${item.value}'`;
    }
    if (item.type === 'date_no_time' && item.value) {
      whereClause += ` AND DATE(${item.name}) = '${item.value}'`;
    }
  });

  sql += whereClause;

  if (sort && sort.type === 'other') {
    sql += ` ORDER BY ${sort.name} ${sort.value}`;
  }

  if (sort && sort.type === 'date') {
    sql += ` ORDER BY ${sort.name} ${sort.value}`;
  }

  if (pageSize && current) {
    sql += ` LIMIT ${pageSize} OFFSET ${pageSize * (current - 1)}`;
  }

  const countSql = `SELECT COUNT(*) AS total FROM ${table_name}${whereClause}`;

  return { sql, countSql };
};

export interface DateTypeEnum {
  date: [Dayjs, Dayjs]
  month: string | number
  quarter: string | number
  year: string | number
  week: string | number
}

export type DataTypeParams<K extends keyof DateTypeEnum> = {
  type: K
  value: DateTypeEnum[K]
};

export const sqlDateTypeFormat = ({
  table_name,
  time_name,
  total_name,
  value_name,
  total_type = 'SUM',
  whereClaus = '',
  type,
  value,
}: {
  /** 表名 */
  table_name: string
  /** 时间依据字段 */
  time_name: string
  /** 统计对象字段 */
  total_name: string
  /** 返回的值字段名字 */
  value_name: string
  /** 统计类型 */
  total_type?: 'SUM' | 'COUNT'
  /** 其他条件 */
  whereClaus?: string
  /** 类型 */
  type: keyof DateTypeEnum
  value: DateTypeEnum[keyof DateTypeEnum]
}) => {
  let sql = '';
  if (type === 'week') {
    sql = `SELECT DATE(${time_name}) AS date,
         ${total_type}(${total_name}) AS ${value_name}
         FROM ${table_name}
         WHERE YEAR(${time_name}) = ${value?.toString()?.substring(0, 4)} AND WEEK(${time_name}) = ${value?.toString()?.substring(5, value?.toString()?.length)} ${whereClaus ? ' AND ' + whereClaus : ''}
         GROUP BY DATE(${time_name})`;
  }
  if (type === 'quarter') {
    sql = `SELECT DATE_FORMAT(${time_name}, '%Y-%m') AS date,
        ${total_type}(${total_name}) AS ${value_name}
         FROM ${table_name}
         WHERE YEAR(${time_name}) = ${value?.toString()?.substring(0, 4)} AND QUARTER(${time_name}) = ${value?.toString()?.substring(5, value?.toString()?.length)} ${whereClaus ? ' AND ' + whereClaus : ''}
         GROUP BY DATE_FORMAT(${time_name}, '%Y-%m')`;
  }
  if (type === 'month') {
    sql = `SELECT DATE(${time_name}) AS date,
         ${total_type}(${total_name}) AS ${value_name}
         FROM ${table_name}
         WHERE DATE_FORMAT(${time_name}, '%Y-%m') = '${value}' ${whereClaus ? ' AND ' + whereClaus : ''}
         GROUP BY DATE(${time_name})`;
  }
  if (type === 'year') {
    sql = `SELECT DATE_FORMAT(${time_name}, '%Y-%m') AS date,
         ${total_type}(${total_name}) AS ${value_name}
         FROM ${table_name}
         WHERE DATE_FORMAT(${time_name}, '%Y') = '${value}' ${whereClaus ? ' AND ' + whereClaus : ''}
         GROUP BY DATE_FORMAT(${time_name}, '%Y-%m')`;
  }
  if (type === 'date') {
    const thisValue = JSON.parse(value?.toString());
    sql = `SELECT DATE(${time_name}) AS date,
         ${total_type}(${total_name}) AS ${value_name} 
         FROM ${table_name}
         WHERE ${time_name} >= '${thisValue?.[0]}' AND ${time_name} <= '${thisValue?.[1]}' ${whereClaus ? ' AND ' + whereClaus : ''}
         GROUP BY DATE(${time_name})`;
  }
  return sql;
};


export const sqlDateFormat = ({
  type,
  value,
  time_name,
}: {
  /** 类型 */
  type: keyof DateTypeEnum
  /** 时间依据字段 */
  time_name: string
  value: DateTypeEnum[keyof DateTypeEnum]
}) => {
  let sql = '';
  if (type === 'week') {
    sql = `YEAR(${time_name}) = ${value?.toString()?.substring(0, 4)} AND WEEK(${time_name}) = ${value?.toString()?.substring(5, value?.toString()?.length)}`;
  }
  if (type === 'quarter') {
    sql = `YEAR(${time_name}) = ${value?.toString()?.substring(0, 4)} AND QUARTER(${time_name}) = ${value?.toString()?.substring(5, value?.toString()?.length)}`;
  }
  if (type === 'month') {
    sql = `DATE_FORMAT(${time_name}, '%Y-%m') = '${value}'`;
  }
  if (type === 'year') {
    sql = `DATE_FORMAT(${time_name}, '%Y') = '${value}'`;
  }
  if (type === 'date') {
    const thisValue = JSON.parse(value?.toString());
    sql = `${time_name} >= '${thisValue?.[0]}' AND ${time_name} <= '${thisValue?.[1]}'`;
  }
  return sql;
};
