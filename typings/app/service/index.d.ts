// This file is created by egg-ts-helper@2.1.0
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
type AnyClass = new (...args: any[]) => any;
type AnyFunc<T = any> = (...args: any[]) => T;
type CanExportFunc = AnyFunc<Promise<any>> | AnyFunc<IterableIterator<any>>;
type AutoInstanceType<T, U = T extends CanExportFunc ? T : T extends AnyFunc ? ReturnType<T> : T> = U extends AnyClass ? InstanceType<U> : U;
import ExportAdminBox from '../../../app/service/admin/box';
import ExportAdminData from '../../../app/service/admin/data';
import ExportAdminLivePlan from '../../../app/service/admin/livePlan';
import ExportAdminLogs from '../../../app/service/admin/logs';
import ExportAdminMaterial from '../../../app/service/admin/material';
import ExportAdminOldProduct from '../../../app/service/admin/old_product';
import ExportAdminOrder from '../../../app/service/admin/order';
import ExportAdminProduct from '../../../app/service/admin/product';
import ExportAdminUser from '../../../app/service/admin/user';
import ExportCustomerBox from '../../../app/service/customer/box';
import ExportCustomerOrder from '../../../app/service/customer/order';
import ExportCustomerProduct from '../../../app/service/customer/product';
import ExportCustomerUser from '../../../app/service/customer/user';

declare module 'egg' {
  interface IService {
    admin: {
      box: AutoInstanceType<typeof ExportAdminBox>;
      data: AutoInstanceType<typeof ExportAdminData>;
      livePlan: AutoInstanceType<typeof ExportAdminLivePlan>;
      logs: AutoInstanceType<typeof ExportAdminLogs>;
      material: AutoInstanceType<typeof ExportAdminMaterial>;
      oldProduct: AutoInstanceType<typeof ExportAdminOldProduct>;
      order: AutoInstanceType<typeof ExportAdminOrder>;
      product: AutoInstanceType<typeof ExportAdminProduct>;
      user: AutoInstanceType<typeof ExportAdminUser>;
    }
    customer: {
      box: AutoInstanceType<typeof ExportCustomerBox>;
      order: AutoInstanceType<typeof ExportCustomerOrder>;
      product: AutoInstanceType<typeof ExportCustomerProduct>;
      user: AutoInstanceType<typeof ExportCustomerUser>;
    }
  }
}
