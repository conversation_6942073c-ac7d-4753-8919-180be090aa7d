// This file is created by egg-ts-helper@2.1.0
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportErrorHandler from '../../../app/middleware/errorHandler';
import ExportJwtCheck from '../../../app/middleware/jwtCheck';
import ExportLogRequest from '../../../app/middleware/logRequest';
import ExportParamCheck from '../../../app/middleware/paramCheck';
import ExportRateLimit from '../../../app/middleware/rateLimit';

declare module 'egg' {
  interface IMiddleware {
    errorHandler: typeof ExportErrorHandler;
    jwtCheck: typeof ExportJwtCheck;
    logRequest: typeof ExportLogRequest;
    paramCheck: typeof ExportParamCheck;
    rateLimit: typeof ExportRateLimit;
  }
}
