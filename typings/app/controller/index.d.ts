// This file is created by egg-ts-helper@2.1.0
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportIndex from '../../../app/controller/index';
import ExportAdminBox from '../../../app/controller/admin/box';
import ExportAdminLivePlan from '../../../app/controller/admin/livePlan';
import ExportAdminLogin from '../../../app/controller/admin/login';
import ExportAdminLogs from '../../../app/controller/admin/logs';
import ExportAdminMaterial from '../../../app/controller/admin/material';
import ExportAdminOldProduct from '../../../app/controller/admin/old_product';
import ExportAdminOrder from '../../../app/controller/admin/order';
import ExportAdminProduct from '../../../app/controller/admin/product';
import ExportAdminTypings from '../../../app/controller/admin/typings';
import ExportAdminUser from '../../../app/controller/admin/user';
import ExportAdminVip from '../../../app/controller/admin/vip';
import ExportCustomerBox from '../../../app/controller/customer/box';
import ExportCustomerOrder from '../../../app/controller/customer/order';
import ExportCustomerProduct from '../../../app/controller/customer/product';
import ExportCustomerUser from '../../../app/controller/customer/user';
import ExportAdminDataCustomData from '../../../app/controller/admin/data/customData';
import ExportAdminDataOrderData from '../../../app/controller/admin/data/orderData';
import ExportAdminDataProductData from '../../../app/controller/admin/data/productData';
import ExportAdminDataUserData from '../../../app/controller/admin/data/userData';

declare module 'egg' {
  interface IController {
    index: ExportIndex;
    admin: {
      box: ExportAdminBox;
      livePlan: ExportAdminLivePlan;
      login: ExportAdminLogin;
      logs: ExportAdminLogs;
      material: ExportAdminMaterial;
      oldProduct: ExportAdminOldProduct;
      order: ExportAdminOrder;
      product: ExportAdminProduct;
      typings: ExportAdminTypings;
      user: ExportAdminUser;
      vip: ExportAdminVip;
      data: {
        customData: ExportAdminDataCustomData;
        orderData: ExportAdminDataOrderData;
        productData: ExportAdminDataProductData;
        userData: ExportAdminDataUserData;
      }
    }
    customer: {
      box: ExportCustomerBox;
      order: ExportCustomerOrder;
      product: ExportCustomerProduct;
      user: ExportCustomerUser;
    }
  }
}
