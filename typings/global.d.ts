declare global {
    type PaginationParam = {
        pageSize?: number;
        current?: number;
    }

    type ApiResponse<T> = {
        code: number,
        success: boolean;
        message: string;
        data: T;
    }

    type PaginationApiResponse = {
        total: number;
        pageSize: number;
        current: number;
        data: any
    };

    type UserType = 'admin' | 'customer'
    
    type StockOperationType = 'increase' | 'decrease'
}

export {}; 